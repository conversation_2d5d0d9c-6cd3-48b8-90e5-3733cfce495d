{"name": "web", "author": "Shanghai Baosight Software Co., Ltd.", "private": true, "type": "module", "scripts": {"pre": "node scripts/ag-grid", "dev": "npm run pre && vite", "build": "npm run pre && vite build"}, "dependencies": {"@ag-grid-community/locale": "^34.0.0", "@ant-design/icons-vue": "^7.0.1", "@heroicons/vue": "^2.2.0", "@tailwindcss/vite": "^4.1.11", "@tanstack/vue-query": "^5.81.5", "@vueuse/core": "^13.4.0", "ag-grid-community": "^34.0.0", "ag-grid-enterprise": "^34.0.0", "ag-grid-vue3": "^34.0.0", "ant-design-vue": "^4.2.6", "axios": "^1.10.0", "dayjs": "^1.11.13", "lodash": "^4.17.21", "mitt": "^3.0.1", "pinia": "^3.0.3", "tailwindcss": "^4.1.11", "three": "^0.177.0", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@tsconfig/node22": "^22.0.2", "@types/lodash": "^4.17.19", "@types/node": "^24.0.7", "@types/three": "^0.177.0", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^7.0.5", "vite-plugin-vue-devtools": "^7.7.7", "vue-tsc": "^2.2.10"}}