<script setup lang="ts">
import * as THREE from 'three'; // Assuming THREE.Vector3 and THREE.Euler are used for camera states
import { computed, nextTick, onMounted, onUnmounted, ref, watch, type PropType } from 'vue';
// import FirstViewControl from './components/FirstViewControl.vue';
import AddDeviceModal from '../left_part/components/AddDeviceModal.vue';
import AddObstacleModal from '../left_part/components/AddObstacleModal.vue';
import MoveControl from './components/MoveControl.vue';
import LineControl from './components/lineControl.vue';
import ScheduleRequestForm from './components/ScheduleRequestForm.vue';

interface LaserSensor {
  id: string;
  name: string;
  relativePosition: { x: number; y: number; z: number }; // 激光在车辆中心点的相对位置
  pitchRange: { min: number; max: number }; // 俯仰角范围
  yawRange: { min: number; max: number }; // 水平角范围
  laserDensity: number; // 激光密度 (每度激光线数量)
  enabled: boolean; // 是否启用
}

interface Model {
  id: string;
  name: string;
  path: string;
  position: { x: number; y: number; z: number };
  rotation: { x: number; y: number; z: number };
  scale: number;
  currentPosition: { x: number; y: number; z: number };
  currentRotation: { x: number; y: number; z: number };
  battery?: number; // 电量百分比 (0-100)
  sensors?: LaserSensor[]; // 多个激光传感器
  showInfoCard?: boolean; // 是否显示信息卡片
}

interface SceneConfig {
  background: number;
  lights: Array<{ type: string; color: number; intensity: number; position?: { x: number; y: number; z: number } }>;
  camera: {
    position: { x: number; y: number; z: number };
    lookAt: { x: number; y: number; z: number };
  };
}

interface LaserSensor {
  id: string;
  name: string;
  relativePosition: { x: number; y: number; z: number };
  pitchRange: { min: number; max: number };
  yawRange: { min: number; max: number };
  laserDensity: number;
  enabled: boolean;
}

interface FirstPersonView {
  enabled: boolean;
  targetModelId: string | null;
  cameraOffset: { x: number; y: number; z: number };
  lookAtOffset: { x: number; y: number; z: number };
  followRotation: boolean;
  followMovement: boolean;
  smoothFollow: boolean;
  updateInterval: number | null;
  originalCameraPosition: THREE.Vector3;
  originalCameraRotation: THREE.Euler;
  originalControlsTarget: THREE.Vector3;
  presets: Record<string, { camera: { x: number; y: number; z: number }; lookAt: { x: number; y: number; z: number } }>;
  currentPreset: string;
}

const props = defineProps({
  loadingStatus: {
    type: Object as PropType<Record<string, 'loaded' | 'loading' | 'error'>>,
    default: () => ({}),
  },
  firstPersonView: {
    type: Object as PropType<FirstPersonView>,
    required: true,
  },
  // 场景配置
  sceneConfig: {
    type: Object as PropType<SceneConfig>,
    default: () => ({
      background: 0xffffff,
      lights: [
        { type: 'ambient', color: 0xffffff, intensity: 2 },
        { type: 'directional', color: 0xffffff, intensity: 2, position: { x: 1, y: 2, z: 3 } },
      ],
      camera: {
        position: { x: 680, y: -430, z: 100 }, // 世界相机初始位置
        lookAt: { x: 90, y: 90, z: 90 }, // 视角朝向
      },
    }),
  },
  totalModels: {
    type: Number,
    default: 0,
  },
  // 模型配置数组
  models: {
    type: Array as PropType<Model[]>,
    default: () => [],
  },
  // SceneManagerRef is passed down to FirstViewControl, so it's not directly used here for its methods
  // but its type is important for the FirstPersonView object.
  sceneManagerRef: {
    type: Object as PropType<any>, // Keeping as any for now as its full structure isn't provided
    required: true,
  },
  model: {
    type: Object,
    default: null,
  },
  modelComponent: {
    type: Object,
    default: null,
  },
  mapInteraction: {
    type: Object,
    required: true,
  },
  websocketSimulator: {
    type: Object,
    default: null,
  },
  pathExecutionSystem: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits([
  'startFirstPersonUpdate',
  'getSelectedModelByIdForFirstPerson',
  'statusChange',
  'positionUpdate',
  'model-selected',
  'movement-started',
  'movement-stopped',
  'perform-energy-simulation',
  'clear-start-point',
  'clear-end-point',
  'panel-collapsed',
  'perception-simulation',
  'vehicle-type-changed',
  'model-added',
  'config-exported',
  'config-imported',
  'scene-settings-changed',
  'info-cards-toggled',
  'start-path-execution',
  'stop-path-execution',
  'update-speed-multiplier',
  'map-loading',
  'map-loaded',
  'obstacle-added',
  'obstacle-removed',
  'set-camera-preset',
  'set-custom-camera-position',
  'smooth-animation-changed',
  'coordinate-request',
  'schedule-submit',
]);
const leftPanelCollapsed = ref(false);
const editingModelId = ref<string | null>(null);
const showSensorConfig = ref<string | null>(null);
const showAddDeviceModal = ref(false);
const showInfoCards = ref(false); // 信息卡片显示开关，默认关闭

// 编辑表单数据
const editForm = ref({
  x: 0,
  z: 0,
  battery: 100,
  rotation: 0,
});

// 传感器配置表单数据
const sensorForm = ref<LaserSensor>({
  id: '',
  name: '',
  relativePosition: { x: 0, y: 0, z: 0 },
  pitchRange: { min: -45, max: 45 },
  yawRange: { min: -180, max: 180 },
  laserDensity: 10, // 每度10条激光线
  enabled: true,
});

const editingSensorIndex = ref<number>(-1); // -1表示新增，>=0表示编辑现有传感器

const rightPanelCollapsed = ref(false);

// 右侧面板的表单数据
const refreshRate = ref(5);

// 车型选择数据
const selectedVehicleType = ref('0'); // 默认选择扇形布料机

// 车型选项定义
const vehicleTypeOptions = [
  { value: '0', label: '扇形布料机' },
  { value: '1', label: '斗轮挖掘机' },
  { value: '2', label: '桥式转载机' },
  { value: '3', label: '中继转载机' },
  { value: '4', label: '移动转载机' }
];

// 地图相关数据
const availableMaps = ref([]);
const selectedMap = ref(null);
const currentMapName = ref('');
const isLoadingMap = ref(false);

// 障碍物相关数据
const obstacles = ref([]);
const isAddingObstacle = ref(false);
const showAddObstacleModalVisible = ref(false);

// 速度倍数控制
const speedMultiplier = ref(1);

// 移动控制相关
const movementControls = ref({
  selectedModelId: 'car7',
  targetPosition: { x: 0, y: 0, z: 0 },
  movementSpeed: 2.0,
  isMoving: false,
  keyboardControlEnabled: true,
});

// 切换面板
const currentPanel = ref(0);

// 调度请求相关数据
const scheduleRequestFormRef = ref(null);
const currentCoordinateRequest = ref<{type: string, processType: number} | null>(null);

const toggleRightPanel = () => {
  rightPanelCollapsed.value = !rightPanelCollapsed.value;

  // 发送面板状态变化事件到父组件
  emit('panel-collapsed', rightPanelCollapsed.value);

  console.log(`📱 右侧面板${rightPanelCollapsed.value ? '收起' : '展开'}`);
};

// 处理坐标请求
const handleCoordinateRequest = (coordinateType: string, processType: number) => {
  currentCoordinateRequest.value = { type: coordinateType, processType };
  emit('coordinate-request', coordinateType, processType);
  console.log(`📍 请求设置坐标: ${coordinateType}, 工艺类型: ${processType}`);
};

// 设置坐标（从父组件调用）
const setCoordinate = (coordinate: {x: number, z: number}) => {
  if (scheduleRequestFormRef.value) {
    // 直接调用调度请求表单的setCoordinate方法
    scheduleRequestFormRef.value.setCoordinate('', coordinate);
    console.log(`✅ 坐标已传递到调度表单:`, coordinate);
  }
};

// 处理调度请求提交
const handleScheduleSubmit = (request: any) => {
  console.log('📋 调度请求提交:', request);
  emit('schedule-submit', request);

  // 这里可以添加提交到后端的逻辑
  alert(`调度请求已提交！\n工艺类型: ${request.processType === 1 ? '转储' : request.processType === 2 ? '装船' : '卸船分料'}\n布料方向: ${request.fabricDirection === 0 ? '从左往右' : '从右往左'}`);
};


const PanelChange = (index) => {
  switch (index) {
    case 0:
      currentPanel.value = 0;
      break;
    case 1:
      currentPanel.value = 1;
      break;
    case 2:
      currentPanel.value = 2;
      break;
    case 3:
      currentPanel.value = 3;
      break;
    case 4:
      currentPanel.value = 4;
      break;
    default:
      break;
  }
}




// 处理场景设置变化
const handleSceneSettingsChanged = settings => {
  emit('scene-settings-changed', settings);
  console.log('场景设置已更新:', settings);
};

// 处理流畅动画设置变化
const handleSmoothAnimationChanged = animationSettings => {
  emit('smooth-animation-changed', animationSettings);
  console.log('流畅动画设置已更新:', animationSettings);
};

// 车型选择变化处理
const handleVehicleTypeChange = () => {
  console.log('车型选择变化:', selectedVehicleType.value);
  emit('vehicle-type-changed', selectedVehicleType.value);
};

// 切换信息卡片显示
const toggleInfoCards = () => {
  showInfoCards.value = !showInfoCards.value;
  console.log('右侧面板 - 信息卡片切换:', showInfoCards.value ? '开启' : '关闭');
  console.log('当前模型数量:', props.models.length);
  emit('info-cards-toggled', showInfoCards.value);
  console.log('事件已发送: info-cards-toggled', showInfoCards.value);
};

// 开始路径执行
const startPathExecution = () => {
  console.log('右侧面板 - 开始路径执行');
  emit('start-path-execution');
};

// 停止路径执行
const stopPathExecution = () => {
  console.log('右侧面板 - 停止路径执行');
  speedMultiplier.value = 1; // 重置速度倍数
  emit('stop-path-execution');
};

// 更新速度倍数
const updateSpeedMultiplier = () => {
  console.log('右侧面板 - 更新执行速度:', speedMultiplier.value + 'x');
  emit('update-speed-multiplier', speedMultiplier.value);
};

// 设置指定速度
const setSpeed = speed => {
  speedMultiplier.value = speed;
  updateSpeedMultiplier();
};

// 能耗仿真
const handleEnergySimulation = () => {
  emit('perform-energy-simulation');
};

// 清除起点
const clearStartPoint = () => {
  emit('clear-start-point');
};

// 清除终点
const clearEndPoint = () => {
  emit('clear-end-point');
};

// 感知模拟相关
const isPerceptionSimulating = ref(false);

// 选中车辆电量消耗数据
const selectedVehicleConsumption = ref(null);

// 处理感知模拟
const handlePerceptionSimulation = async () => {
  if (isPerceptionSimulating.value) return;

  isPerceptionSimulating.value = true;

  try {
    console.log('开始感知模拟...');

    // 发送感知模拟事件到父组件
    emit('perception-simulation');

    // 模拟感知处理时间
    await new Promise(resolve => setTimeout(resolve, 1000));

    console.log('感知模拟完成');

  } catch (error) {
    console.error('感知模拟失败:', error);
    alert('感知模拟失败：' + error.message);
  } finally {
    isPerceptionSimulating.value = false;
  }
};

// 获取电量颜色样式类
const getBatteryColorClass = (power) => {
  if (power >= 50) return 'text-green-400';
  if (power >= 20) return 'text-orange-400';
  return 'text-red-400';
};

// 获取电量进度条样式类
const getBatteryProgressClass = (power) => {
  if (power >= 50) return 'bg-gradient-to-r from-green-500 to-green-400';
  if (power >= 20) return 'bg-gradient-to-r from-orange-500 to-orange-400';
  return 'bg-gradient-to-r from-red-500 to-red-400';
};

// 更新选中车辆的电量消耗信息
const updateSelectedVehicleConsumption = (vehicleData) => {
  if (!vehicleData) {
    selectedVehicleConsumption.value = null;
    return;
  }

  selectedVehicleConsumption.value = {
    deviceId: vehicleData.deviceId,
    currentPower: vehicleData.currentPower || 100,
    totalConsume: vehicleData.totalConsume || 0,
    consumeRate: vehicleData.consumeRate || 0,
    lastUpdateTime: Date.now()
  };

  console.log('更新选中车辆消耗信息:', selectedVehicleConsumption.value);
};

// 加载地图配置
const loadMapConfig = async () => {
  try {
    const response = await fetch('/src/views/route_simulatton/left_part/components/map.json');
    const mapData = await response.json();
    availableMaps.value = mapData;
    console.log('地图配置加载成功:', mapData);
  } catch (error) {
    console.error('加载地图配置失败:', error);
    availableMaps.value = [];
  }
};

// 处理地图选择
const handleMapSelection = () => {
  console.log('选择地图:', selectedMap.value);
  if (selectedMap.value) {
    console.log('选中地图详情:', {
      id: selectedMap.value.id,
      name: selectedMap.value.name,
      path: selectedMap.value.path,
      hasValidPath: !!selectedMap.value.path,
    });
  }
};

// 获取地图状态
const getMapStatus = map => {
  if (!map.path) return '无效';
  if (!map.name) return '无名称';
  if (!map.id) return '无ID';
  return '可用';
};

// 加载选中的地图
const loadSelectedMap = async () => {
  if (!selectedMap.value || isLoadingMap.value) return;

  isLoadingMap.value = true;

  try {
    console.log('开始加载地图:', selectedMap.value.name);
    console.log('地图配置:', selectedMap.value);

    // 发送地图开始加载事件到父组件
    emit('map-loading', selectedMap.value);

    // 发送地图配置到父组件进行实际加载
    emit('map-loaded', selectedMap.value);

    // 更新当前地图名称
    currentMapName.value = selectedMap.value.name;

    console.log('地图配置已传递到主页面');
  } catch (error) {
    console.error('地图加载失败:', error);
    alert('地图加载失败：' + error.message);
  } finally {
    isLoadingMap.value = false;
  }
};

// 显示添加障碍物模态框
const showAddObstacleModal = () => {
  showAddObstacleModalVisible.value = true;
};

// 处理添加障碍物（从模态框确认）
const handleAddObstacle = async obstacleFormData => {
  if (isAddingObstacle.value) return;

  isAddingObstacle.value = true;

  try {
    console.log('开始添加障碍物...', obstacleFormData);

    // 生成障碍物数据
    const obstacleData = {
      id: `obstacle_${Date.now()}`,
      name: obstacleFormData.name,
      type: 'obstacle',
      path: '/models/person2.glb',
      position: { ...obstacleFormData.position },
      rotation: { ...obstacleFormData.rotation },
      scale: { ...obstacleFormData.scale },
      color: obstacleFormData.color,
      battery: obstacleFormData.battery,
      consume: 0,
      // 默认移动路径（隐式加载）
      defaultPath: [
        {
          position: { x: obstacleFormData.position.x, y: obstacleFormData.position.y, z: obstacleFormData.position.z },
          rotation: { x: obstacleFormData.rotation.x, y: obstacleFormData.rotation.y, z: obstacleFormData.rotation.z },
          duration: 2000,
          power: obstacleFormData.battery - 10,
        },
        {
          position: {
            x: obstacleFormData.position.x + 20,
            y: obstacleFormData.position.y,
            z: obstacleFormData.position.z + 20,
          },
          rotation: {
            x: obstacleFormData.rotation.x,
            y: obstacleFormData.rotation.y + Math.PI / 2,
            z: obstacleFormData.rotation.z,
          },
          duration: 3000,
          power: obstacleFormData.battery - 30,
        },
        {
          position: {
            x: obstacleFormData.position.x - 20,
            y: obstacleFormData.position.y,
            z: obstacleFormData.position.z + 20,
          },
          rotation: {
            x: obstacleFormData.rotation.x,
            y: obstacleFormData.rotation.y + Math.PI,
            z: obstacleFormData.rotation.z,
          },
          duration: 3000,
          power: obstacleFormData.battery - 50,
        },
        {
          position: { x: obstacleFormData.position.x, y: obstacleFormData.position.y, z: obstacleFormData.position.z },
          rotation: {
            x: obstacleFormData.rotation.x,
            y: obstacleFormData.rotation.y + Math.PI * 1.5,
            z: obstacleFormData.rotation.z,
          },
          duration: 2000,
          power: obstacleFormData.battery - 70,
        },
      ],
    };

    // 添加到障碍物列表
    obstacles.value.push(obstacleData);

    // 发送事件到父组件
    emit('obstacle-added', obstacleData);

    console.log('障碍物添加成功:', obstacleData.name);
  } catch (error) {
    console.error('添加障碍物失败:', error);
    alert('添加障碍物失败：' + error.message);
  } finally {
    isAddingObstacle.value = false;
  }
};

// 删除障碍物
const removeObstacle = obstacleId => {
  const index = obstacles.value.findIndex(obs => obs.id === obstacleId);
  if (index !== -1) {
    const obstacle = obstacles.value[index];
    obstacles.value.splice(index, 1);

    // 发送事件到父组件
    emit('obstacle-removed', obstacleId);

    console.log('障碍物已删除:', obstacle.name);
  }
};

// 打开添加设备弹框
const openAddDeviceModal = () => {
  showAddDeviceModal.value = true;
};

// 关闭添加设备弹框
const closeAddDeviceModal = () => {
  showAddDeviceModal.value = false;
};

// 确认添加设备
const confirmAddDevice = (newModel) => {
  // 为新模型添加信息卡片显示状态（默认开启）
  newModel.showInfoCard = true;

  // 添加到models数组
  props.models.push(newModel);

  // 发送事件到父组件
  emit('model-added', newModel);

  // 关闭弹框
  closeAddDeviceModal();

  console.log('设备添加成功:', newModel.name);
};

// LineControl 组件事件处理
const handleRouteStatusChange = data => {
  emit('statusChange', data);
};

const handleRoutePositionUpdate = data => {
  emit('positionUpdate', data);
};

// 编辑功能
const startEdit = (model: any) => {
  editingModelId.value = model.id;
  editForm.value = {
    x: model.currentPosition.x,
    z: model.currentPosition.z,
    battery: model.battery || 100,
    rotation: model.currentRotation.y,
  };
};

const cancelEdit = () => {
  editingModelId.value = null;
};

const saveEdit = (model: any) => {
  // 更新模型数据
  model.currentPosition.x = editForm.value.x;
  model.currentPosition.z = editForm.value.z;
  model.battery = editForm.value.battery;
  model.currentRotation.y = editForm.value.rotation;

  // 退出编辑模式
  editingModelId.value = null;

  // 这里可以添加保存到后端的逻辑
  console.log('Model updated:', model);
};

// 传感器配置功能
const openSensorConfig = (model: any) => {
  showSensorConfig.value = model.id;
  // 确保模型有sensors数组
  if (!model.sensors) {
    model.sensors = [];
  }
};

const closeSensorConfig = () => {
  showSensorConfig.value = null;
  editingSensorIndex.value = -1;
};

const addNewSensor = (model: any) => {
  const newSensorId = `sensor_${Date.now()}`;
  sensorForm.value = {
    id: newSensorId,
    name: `激光传感器${(model.sensors?.length || 0) + 1}`,
    relativePosition: { x: 0, y: 0, z: 0 },
    pitchRange: { min: -45, max: 45 },
    yawRange: { min: -180, max: 180 },
    laserDensity: 10,
    enabled: true,
  };
  editingSensorIndex.value = -1; // 新增模式
};

const editSensor = (model: any, index: number) => {
  const sensor = model.sensors[index];
  sensorForm.value = { ...sensor };
  editingSensorIndex.value = index;
};

const saveSensor = (model: any) => {
  if (!model.sensors) {
    model.sensors = [];
  }

  if (editingSensorIndex.value === -1) {
    // 新增传感器
    model.sensors.push({ ...sensorForm.value });
  } else {
    // 编辑现有传感器
    model.sensors[editingSensorIndex.value] = { ...sensorForm.value };
  }

  editingSensorIndex.value = -1;
  console.log('Sensor saved:', sensorForm.value);
};

const deleteSensor = (model: any, index: number) => {
  if (model.sensors && index >= 0 && index < model.sensors.length) {
    model.sensors.splice(index, 1);
    console.log('Sensor deleted at index:', index);
  }
};

const toggleSensorEnabled = (sensor: LaserSensor) => {
  sensor.enabled = !sensor.enabled;
  console.log('Sensor toggled:', sensor.name, sensor.enabled);
};

// 获取模型状态
const getModelStatus = (model: any) => {
  const loadingStatus = props.loadingStatus[model.id];
  const battery = model.battery || 100;

  if (loadingStatus === 'error') return { status: '错误', color: 'red' };
  if (loadingStatus === 'loading') return { status: '加载中', color: 'yellow' };
  if (battery < 30) return { status: '低电量', color: 'orange' };
  return { status: '在线', color: 'green' };
};

// 切换单个模型的信息卡片显示状态
const toggleModelInfoCard = (model: Model) => {
  const oldState = model.showInfoCard;
  model.showInfoCard = !model.showInfoCard;

  // 如果用户开启了单个设备的信息卡片，但全局开关是关闭的，自动开启全局开关
  if (model.showInfoCard && !showInfoCards.value) {
    console.log('自动开启全局信息卡片开关');
    showInfoCards.value = true;
    emit('info-cards-toggled', showInfoCards.value);
  }

  console.log(`模型 ${model.name} 信息卡片状态变化:`, {
    modelId: model.id,
    oldState: oldState,
    newState: model.showInfoCard,
    globalSwitch: showInfoCards.value,
    action: model.showInfoCard ? '显示' : '隐藏',
  });

  // 触发响应式更新
  nextTick(() => {
    console.log(`nextTick 后模型 ${model.name} 的 showInfoCard:`, model.showInfoCard);
    console.log(`nextTick 后全局开关状态:`, showInfoCards.value);
  });
};

// 第一人称视角控制
const enableFirstPersonView = (modelId: string) => {
  console.log('props.sceneManagerRef', props.sceneManagerRef);
  if (!props.sceneManagerRef) {
    console.warn('SceneManager not available');
    return;
  }

  if (props.firstPersonView.enabled) {
    disableFirstPersonView();
  }

  const getSelectedModelByIdForFirstPerson = (modelId: string) => {
    if (!props.sceneManagerRef) return null;
    try {
      const loadedModels = props.sceneManagerRef.loadedModels;
      if (loadedModels && loadedModels[modelId]) {
        return loadedModels[modelId];
      }
      const modelRef = props.sceneManagerRef.modelRefs?.[modelId];
      return modelRef?.getModel?.() || null;
    } catch (error) {
      console.error('Error getting model for first person view:', error);
      return null;
    }
  };

  const targetModel = getSelectedModelByIdForFirstPerson(modelId);
  const camera = props.sceneManagerRef?.getCamera?.();
  if (!targetModel || !camera) {
    console.warn(`Model ${modelId} or camera not found for first person view`);
    return;
  }

  props.firstPersonView.originalCameraPosition.copy(camera.position);
  props.firstPersonView.originalCameraRotation.copy(camera.rotation);

  const controls = props.sceneManagerRef.getControls?.();
  if (controls) {
    props.firstPersonView.originalControlsTarget.copy(controls.target);
  }

  if (props.sceneManagerRef.setFirstPersonMode) {
    props.sceneManagerRef.setFirstPersonMode(true);
  }

  movementControls.value.selectedModelId = modelId;
  props.firstPersonView.enabled = true;
  props.firstPersonView.targetModelId = modelId;

  emit('startFirstPersonUpdate');
  console.log(`First person view enabled for model: ${modelId}`);
  console.log(`Movement controls now target model: ${modelId}`);
};

const disableFirstPersonView = () => {
  if (!props.firstPersonView.enabled) return;

  const camera = props.sceneManagerRef?.getCamera?.();
  const controls = props.sceneManagerRef?.getControls?.();

  if (props.sceneManagerRef.setFirstPersonMode) {
    props.sceneManagerRef.setFirstPersonMode(false);
  }

  if (camera) {
    camera.position.copy(props.firstPersonView.originalCameraPosition);
    camera.rotation.copy(props.firstPersonView.originalCameraRotation);
  }

  if (controls) {
    controls.target.copy(props.firstPersonView.originalControlsTarget);
    controls.update();
  }

  props.firstPersonView.enabled = false;
  props.firstPersonView.targetModelId = null;

  if (props.firstPersonView.updateInterval) {
    cancelAnimationFrame(props.firstPersonView.updateInterval);
    props.firstPersonView.updateInterval = null;
  }

  console.log('First person view disabled and camera restored');
};

const toggleFirstPersonView = (id: string) => {
  if (props.firstPersonView.enabled && props.firstPersonView.targetModelId === id) {
    disableFirstPersonView();
  } else {
    enableFirstPersonView(id);
  }
};

// 相机设置相关方法
const setCameraPreset = (presetName: string) => {
  console.log('设置相机预设:', presetName);
  emit('set-camera-preset', presetName);
};

const updateCustomCameraPosition = () => {
  const cameraPos = {
    x: props.firstPersonView.cameraOffset?.x || 0,
    y: props.firstPersonView.cameraOffset?.y || 5,
    z: props.firstPersonView.cameraOffset?.z || 0,
  };
  const lookAtPos = {
    x: props.firstPersonView.lookAtOffset?.x || 0,
    y: props.firstPersonView.lookAtOffset?.y || 5,
    z: props.firstPersonView.lookAtOffset?.z || -10,
  };

  console.log('更新自定义相机位置:', { camera: cameraPos, lookAt: lookAtPos });
  emit('set-custom-camera-position', cameraPos, lookAtPos);
};



// Initialize firstPersonView with required THREE objects
const firstPersonView = ref<FirstPersonView>({
  enabled: false,
  targetModelId: null,
  cameraOffset: { x: 0, y: 5, z: 0 },
  lookAtOffset: { x: 0, y: 5, z: -10 },
  followRotation: true,
  followMovement: true,
  smoothFollow: true,
  originalCameraPosition: new THREE.Vector3(),
  originalCameraRotation: new THREE.Euler(),
  originalControlsTarget: new THREE.Vector3(),
  updateInterval: null,
  presets: {
    '驾驶舱': { camera: { x: 0, y: 2, z: 0 }, lookAt: { x: 0, y: 2, z: -5 } },
    '第三人称': { camera: { x: 0, y: 5, z: 10 }, lookAt: { x: 0, y: 2, z: 0 } },
    '俯视': { camera: { x: 0, y: 20, z: 0 }, lookAt: { x: 0, y: 0, z: 0 } },
    '侧视': { camera: { x: 10, y: 5, z: 0 }, lookAt: { x: 0, y: 2, z: 0 } }
  },
  currentPreset: '第三人称'
});

// 模型移除功能已移至左侧面板

const availableModelsForControl = computed(() => {
  return props.models.map(model => ({
    id: model.id,
    name: model.name,
    status: props.loadingStatus[model.id] || 'loading',
  }));
});

// 配置导入导出功能已移至左侧面板

// --- Custom Scrollbar Logic ---
const scrollContentRef = ref<HTMLElement | null>(null);
const scrollIndicatorRef = ref<HTMLElement | null>(null);
const indicatorHeight = ref(0);
const indicatorTop = ref(0);

const updateScrollIndicator = () => {
  const contentEl = scrollContentRef.value;
  const indicatorEl = scrollIndicatorRef.value;
  if (contentEl && indicatorEl) {
    const { scrollTop, scrollHeight, clientHeight } = contentEl;
    if (scrollHeight <= clientHeight) {
      // No scrollbar needed if content fits
      indicatorHeight.value = 0;
      indicatorTop.value = 0;
      indicatorEl.style.height = `0px`;
      indicatorEl.style.transform = `translateY(0px)`;
      return;
    }
    const scrollRatio = clientHeight / scrollHeight;
    indicatorHeight.value = clientHeight * scrollRatio;
    const scrollableHeight = scrollHeight - clientHeight;
    const indicatorTravelHeight = clientHeight - indicatorHeight.value;
    indicatorTop.value = (scrollTop / scrollableHeight) * indicatorTravelHeight;
    indicatorEl.style.height = `${indicatorHeight.value}px`;
    indicatorEl.style.transform = `translateY(${indicatorTop.value}px)`;
  }
};

// 处理移动控制事件
const handleModelSelected = (modelId) => {
  emit('model-selected', modelId);
};

const handleMovementStarted = (data) => {
  emit('movement-started', data);
};

const handleMovementStopped = (data) => {
  emit('movement-stopped', data);
};

const handleScroll = () => {
  updateScrollIndicator();
};

onMounted(async () => {
  // Wait for DOM to render before calculating scrollbar
  nextTick(() => {
    updateScrollIndicator();
  });
  // Add resize listener to update scrollbar if content size changes
  window.addEventListener('resize', updateScrollIndicator);

  // 加载地图配置
  await loadMapConfig();
});

onUnmounted(() => {
  window.removeEventListener('resize', updateScrollIndicator);
});

// Watch for changes in models array length to re-calculate scrollbar
watch(
  () => props.models.length,
  () => {
    nextTick(updateScrollIndicator);
  },
);

// 添加缺失的变量
const totalModels = ref(props.models.length);

// 监听models变化更新totalModels
watch(() => props.models.length, (newLength) => {
  totalModels.value = newLength;
});

// 暴露方法给父组件调用
defineExpose({
  updateSelectedVehicleConsumption,
  setCoordinate
});
</script>

<template>
  <!-- <div
    :class="[
      'absolute top-0 right-0 h-[calc(100vh-5rem)] transition-all duration-300 ease-in-out z-10',
      rightPanelCollapsed ? 'w-12' : 'w-80',
    ]"
  > -->
  <div
    :class="[
      'right_part absolute top-0 right-0 h-[calc(95vh-5rem)] transition-all duration-300 ease-in-out z-101',
      rightPanelCollapsed ? 'w-0' : 'w-110',
    ]"
  >
    <!-- Collapse Menu -->
    <div class="menu_btns gap-2 absolute top-0 z-20 w-8 h-45 bg-slate-800/80 backdrop-blur-sm rounded flex items-center justify-center text-white shadow-lg transition-all duration-200">
      <button
        @click="toggleRightPanel"
        :class="[
          'w-8 h-8 flex items-center justify-center hover:bg-slate-700/90 cursor-pointer',
          rightPanelCollapsed ? 'right-2' : 'left-2',
        ]"
      >
        <svg
          class="w-4 h-4 transition-transform duration-200"
          :class="{ 'rotate-180': !rightPanelCollapsed }"
          fill="currentColor"
          viewBox="0 0 24 24"
        >
          <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z" />
        </svg>
      </button>
      <button
        @click="PanelChange(0)"
        :class="[
          'w-8 h-8 flex items-center justify-center hover:bg-slate-700/90 cursor-pointer',
          rightPanelCollapsed ? 'right-2' : 'left-2',
        ]"
      >
        <svg
          t="1753340750694"
          class="icon w-6 h-6"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="10048"
        >
          <path d="M512 938.4C276.7 938.4 85.3 747 85.3 511.8S276.7 85.1 512 85.1s426.7 191.4 426.7 426.7S747.3 938.4 512 938.4z m0-775.7c-192.5 0-349.1 156.6-349.1 349.1S319.5 860.9 512 860.9s349.1-156.6 349.1-349.1S704.5 162.7 512 162.7z" fill="#ffffff" p-id="10049" data-spm-anchor-id="a313x.search_index.0.i7.1cc13a81cLJZuJ" class="selected"></path>
          <path d="M512 205.4l128 128H384l128-128z m0 612.8l128-128H384l128 128zM205.6 511.8l128-128v256l-128-128z m612.8 0l-128-128v256l128-128z" fill="#ffffff" p-id="10050" data-spm-anchor-id="a313x.search_index.0.i8.1cc13a81cLJZuJ" class="selected"></path>
        </svg>
        
      </button>
      <button
        @click="PanelChange(1)"
        :class="[
          'w-8 h-8 flex items-center justify-center hover:bg-slate-700/90 cursor-pointer',
          rightPanelCollapsed ? 'right-2' : 'left-2',
        ]"
      >
        <svg
          t="1753340581414"
          class="icon w-6 h-6" 
          viewBox="0 0 1024 1024" 
          version="1.1" 
          xmlns="http://www.w3.org/2000/svg" 
          p-id="8770" 
        >
          <path d="M786.432 197.632c-43.008 0-78.336 34.816-78.336 77.824 0 43.008 34.816 77.824 78.336 77.824 43.008 0 78.336-34.816 78.336-77.824-0.512-43.008-35.328-77.824-78.336-77.824z m0 107.008c-14.848-1.024-26.624-12.8-27.136-27.648-0.512-15.872 11.264-29.184 27.136-30.208 15.36 0.512 27.136 12.8 27.648 28.16 0.512 15.872-11.776 28.672-27.648 29.696z" fill="#ffffff" p-id="8771" data-spm-anchor-id="a313x.search_index.0.i5.1cc13a81cLJZuJ" class="selected"></path>
          <path d="M1000.96 232.96c-4.096-19.968-17.92-33.792-35.84-33.792h-1.536c-18.432 0-33.28-14.848-33.28-32.768 0-3.584 1.536-9.216 2.56-11.776v-0.512c7.68-18.432 1.536-39.936-14.336-51.2l-52.224-28.672-2.56-1.024c-16.384-6.656-35.84-3.072-49.152 9.728-7.168 7.68-21.504 16.896-27.648 16.896-6.144 0-20.48-9.216-28.16-17.408-12.8-12.8-31.744-16.384-49.152-9.728L655.36 101.888l-2.56 1.536c-16.384 11.264-22.528 32.768-14.336 51.2 1.024 2.56 3.072 8.192 3.072 11.776 0 17.92-14.848 32.768-33.28 32.768h-1.536c-17.408 0-32.256 13.312-36.352 33.28 0 0-4.608 23.552-4.608 42.496 0 7.68 0.512 17.408 2.048 28.16 1.024 6.656 1.536 10.752 2.56 13.824 4.096 19.968 17.92 33.792 35.84 33.792h2.048c18.432 0 33.28 14.848 33.28 32.768 0 3.584-2.048 9.216-3.072 12.288-7.68 17.92-2.048 39.424 13.824 50.688l51.712 28.672 2.56 1.024c16.896 7.168 36.352 3.072 49.664-10.752 6.656-7.168 21.504-17.92 28.16-17.92 5.632 0 19.968 9.216 28.672 18.432 8.192 8.704 19.968 13.824 32.768 13.824 5.632 0 11.264-1.024 16.384-3.072l53.248-29.184 2.56-1.536c16.384-11.264 22.528-32.768 14.336-51.2-1.024-2.56-3.072-8.192-3.072-11.776 0-17.92 14.848-32.768 33.28-32.768h1.536c17.408 0.512 32.256-13.312 36.352-33.28 0 0 4.608-23.552 4.608-42.496 0.512-17.408-3.584-39.424-4.096-41.472z m-131.584 168.448l-22.016 11.776c-2.56-2.56-6.144-5.12-10.24-8.192-17.92-13.312-35.328-19.456-52.224-19.456-16.896 0-34.304 6.656-51.712 19.456-3.584 2.56-6.656 5.12-10.24 7.68l-20.48-11.264c1.536-6.656 2.56-13.312 2.56-19.456 0-43.52-30.208-80.896-73.216-90.112-0.512-5.12-1.024-11.264-1.024-16.384s0.512-11.776 1.024-16.384c42.496-9.216 73.216-46.08 73.216-90.112 0-6.144-1.024-12.288-2.56-18.944l23.04-12.288c3.072 2.56 6.656 5.12 10.24 7.68 17.408 12.288 34.816 18.432 51.2 18.432s33.28-6.144 50.688-17.92c3.584-2.56 6.656-5.12 10.24-7.68l21.504 11.776c-1.536 6.656-2.56 13.312-2.56 19.456 0 43.52 30.208 80.896 73.216 90.112 0.512 5.12 1.024 11.264 1.024 16.384s-0.512 11.264-1.024 16.384c-42.496 9.216-73.216 46.08-73.216 90.112 0.512 5.632 1.024 12.288 2.56 18.944z" fill="#ffffff" p-id="8772" data-spm-anchor-id="a313x.search_index.0.i4.1cc13a81cLJZuJ" class="selected"></path>
          <path d="M909.824 505.856v211.968c-0.512 27.648-22.528 49.664-50.176 49.664h-7.168c-17.408-61.44-74.24-106.496-140.8-106.496-65.024 0-120.32 42.496-139.264 101.376H491.52c-16.384-8.192-27.648-25.088-27.648-44.544V293.888c0-27.136 22.016-49.664 49.664-49.664h14.336v-81.92h-15.36c-72.704 0-131.584 58.88-131.584 131.584v-25.088H175.104L9.216 434.688v409.6h90.624c16.384 62.976 73.728 110.08 141.824 110.08s125.44-46.592 141.824-110.08h93.184c11.264 3.072 23.552 5.12 36.352 5.12h57.856c17.92 60.416 74.24 104.96 140.288 104.96s122.368-44.544 140.288-104.96h7.68c72.704 0 131.584-58.88 131.584-131.584V505.856h-80.896zM91.136 512h182.784v43.52H91.136V512z m150.528 360.448c-35.328 0-64.512-28.672-64.512-64.512s29.184-64.512 64.512-64.512 64.512 29.184 64.512 64.512c0 35.328-29.184 64.512-64.512 64.512z m143.872-35.84c0-1.024 0.512-1.536 0.512-2.56 0 0.512 0 1.536-0.512 2.56z m-4.608-74.24c-18.944-58.88-74.24-101.376-139.264-101.376s-120.32 42.496-139.264 101.376h-11.264v-155.648h233.984V460.8H98.816l110.08-110.08h172.544v367.104c0 15.36 2.56 30.72 7.68 44.544h-8.192z m330.24 110.08c-35.328 0-64.512-28.672-64.512-64.512s29.184-64.512 64.512-64.512 64.512 29.184 64.512 64.512c0 35.328-29.184 64.512-64.512 64.512z" p-id="8773" data-spm-anchor-id="a313x.search_index.0.i3.1cc13a81cLJZuJ" class="selected" fill="#ffffff"></path>
        </svg>
      </button>
      <button
        @click="PanelChange(2)"
        :class="[
          'w-8 h-8 flex items-center justify-center hover:bg-slate-700/90 cursor-pointer',
          rightPanelCollapsed ? 'right-2' : 'left-2',
        ]"
      >
        <svg
          t="1753340879535"
          class="icon w-6 h-6" 
          viewBox="0 0 1047 1024" 
          version="1.1" 
          xmlns="http://www.w3.org/2000/svg" 
          p-id="11197" 
        >
          <path d="M950.76331 1017.431687h-19.704939a19.704939 19.704939 0 0 1 0-39.409878h19.704939a19.704939 19.704939 0 0 1 0 39.409878m-208.872354 0h-37.439384a19.704939 19.704939 0 0 1 0-39.409878h37.439384a19.704939 19.704939 0 0 1 0 39.409878m114.288646 0h-37.439384a19.704939 19.704939 0 0 1 0-39.409878h37.439384a18.588326 18.588326 0 0 1 19.704939 19.704939 19.704939 19.704939 0 0 1-19.704939 19.704939m-520.210391-394.098781h-37.439384a19.704939 19.704939 0 0 1 0-39.409878h37.439384a18.588326 18.588326 0 0 1 19.704939 19.704939 19.704939 19.704939 0 0 1-19.704939 19.704939m112.318153 0h-37.439385a19.704939 19.704939 0 0 1 0-39.409878h37.439385a19.704939 19.704939 0 0 1 0 39.409878m94.583707 49.262347a23.514561 23.514561 0 0 1-17.734445-9.852469 208.675305 208.675305 0 0 0-19.704939-23.645927 19.704939 19.704939 0 1 1 23.645927-31.527903 159.150225 159.150225 0 0 1 29.557408 33.498397 19.704939 19.704939 0 0 1-7.881975 27.586915c0 3.940988-3.940988 3.940988-7.881976 3.940987m-5.911482 106.406671a27.981013 27.981013 0 0 1-11.822963-3.940988 21.215651 21.215651 0 0 1-3.940988-27.586914 69.098653 69.098653 0 0 0 11.822964-27.586915 19.376523 19.376523 0 0 1 37.439384 9.85247 120.331495 120.331495 0 0 1-19.704939 41.380372c0 5.911482-7.881976 7.881976-13.793458 7.881975m-287.69211 41.380372h-37.439384a19.704939 19.704939 0 0 1 0-39.409878h37.439384a19.704939 19.704939 0 0 1 0 39.409878m112.318153 0h-37.439384a19.704939 19.704939 0 0 1 0-39.409878h37.439384a19.704939 19.704939 0 1 1 0 39.409878m96.554201 0h-19.704939a19.704939 19.704939 0 0 1 0-39.409878h19.704939a33.56408 33.56408 0 0 0 13.793458-1.970494 20.427453 20.427453 0 0 1 23.645926 15.763952 21.60975 21.60975 0 0 1-15.763951 23.645926 88.34381 88.34381 0 0 1-21.675433 1.970494m-358.629891 3.940988a20.755869 20.755869 0 0 1-19.704939-15.763951 20.296087 20.296087 0 0 1 13.793458-23.645927 152.12213 152.12213 0 0 1 29.557408-3.940988h13.793457a19.704939 19.704939 0 0 1 0 39.409878h-13.793457a67.062476 67.062476 0 0 0-19.704939 1.970494 6.568313 6.568313 0 0 0-3.940988 1.970494m-72.908274 76.849263h-3.940988a19.704939 19.704939 0 0 1-15.763951-21.675433 95.568954 95.568954 0 0 1 15.763951-41.380372 19.947967 19.947967 0 1 1 33.498396 21.675432 47.094804 47.094804 0 0 0-9.852469 27.586915 19.704939 19.704939 0 0 1-19.704939 13.793458m41.380372 98.524695a27.981013 27.981013 0 0 1-11.822964-3.940988 96.357152 96.357152 0 0 1-31.527902-31.527902 19.947967 19.947967 0 0 1 33.498396-21.675433 70.280949 70.280949 0 0 0 19.704939 21.675433 19.704939 19.704939 0 0 1 5.911482 27.586914 17.931495 17.931495 0 0 1-15.763951 7.881976m108.377165 17.734445h-37.439385a19.704939 19.704939 0 1 1 0-39.409878h37.439385a19.704939 19.704939 0 0 1 0 39.409878m114.288646 0h-37.439384a19.704939 19.704939 0 0 1 0-39.409878h37.439384a18.588326 18.588326 0 0 1 19.704939 19.704939 19.704939 19.704939 0 0 1-19.704939 19.704939m112.318153 0h-37.439384a19.704939 19.704939 0 1 1 0-39.409878h37.439384a19.704939 19.704939 0 1 1 0 39.409878m114.288646 0h-39.409878a19.704939 19.704939 0 0 1 0-39.409878h39.409878a18.588326 18.588326 0 0 1 19.704939 19.704939 19.704939 19.704939 0 0 1-19.704939 19.704939m112.318153 0h-37.439384a19.704939 19.704939 0 1 1 0-39.409878h37.439384a19.704939 19.704939 0 1 1 0 39.409878m-407.892239-394.098781h-19.704939a19.704939 19.704939 0 0 1 0-39.409878h19.704939a19.704939 19.704939 0 1 1 0 39.409878" fill="#ffffff" p-id="11198" data-spm-anchor-id="a313x.search_index.0.i13.1cc13a81cLJZuJ" class="selected"></path>
          <path d="M950.76331 1024h-19.704939a26.273252 26.273252 0 0 1 0-52.546504h19.704939a26.273252 26.273252 0 0 1 0 52.546504z m-19.704939-39.409878a13.136626 13.136626 0 0 0 0 26.273252h19.704939a13.136626 13.136626 0 0 0 0-26.273252z m-74.878769 39.409878h-37.439384a26.273252 26.273252 0 0 1 0-52.546504h37.439384a25.090956 25.090956 0 0 1 26.273252 26.273252 26.273252 26.273252 0 0 1-26.273252 26.273252z m-37.439384-39.409878a13.136626 13.136626 0 0 0 0 26.273252h37.439384a13.136626 13.136626 0 0 0 13.136626-13.136626 12.085696 12.085696 0 0 0-13.136626-13.136626z m-76.849262 39.409878h-37.439384a26.273252 26.273252 0 0 1 0-52.546504h37.439384a26.273252 26.273252 0 0 1 0 52.546504z m-37.439384-39.409878a13.136626 13.136626 0 0 0 0 26.273252h37.439384a13.136626 13.136626 0 0 0 0-26.273252z m-74.878769 39.409878h-37.439384a26.273252 26.273252 0 1 1 0-52.546504h37.439384a26.273252 26.273252 0 1 1 0 52.546504z m-37.439384-39.409878a13.136626 13.136626 0 1 0 0 26.273252h37.439384a13.136626 13.136626 0 1 0 0-26.273252z m-74.878769 39.409878h-39.409878a26.273252 26.273252 0 0 1 0-52.546504h39.409878a25.090956 25.090956 0 0 1 26.273253 26.273252 26.273252 26.273252 0 0 1-26.273253 26.273252z m-39.409878-39.409878a13.136626 13.136626 0 0 0 0 26.273252h39.409878a13.136626 13.136626 0 0 0 13.136626-13.136626 12.085696 12.085696 0 0 0-13.136626-13.136626z m-74.878768 39.409878h-37.439384a26.273252 26.273252 0 1 1 0-52.546504h37.439384a26.273252 26.273252 0 1 1 0 52.546504z m-37.439384-39.409878a13.136626 13.136626 0 1 0 0 26.273252h37.439384a13.136626 13.136626 0 1 0 0-26.273252z m-74.878769 39.409878h-37.439384a26.273252 26.273252 0 0 1 0-52.546504h37.439384a25.156639 25.156639 0 0 1 26.273252 26.273252 26.273252 26.273252 0 0 1-26.273252 26.273252z m-37.439384-39.409878a13.136626 13.136626 0 0 0 0 26.273252h37.439384a13.136626 13.136626 0 0 0 13.136626-13.136626 12.085696 12.085696 0 0 0-13.136626-13.136626z m-76.849262 39.409878h-37.439385a26.273252 26.273252 0 1 1 0-52.546504h37.439385a26.273252 26.273252 0 0 1 0 52.546504z m-37.439385-39.409878a13.136626 13.136626 0 1 0 0 26.273252h37.439385a13.136626 13.136626 0 0 0 0-26.273252z m-70.93778 21.675433a33.432713 33.432713 0 0 1-14.713021-4.597819l-0.722515-0.459782a103.385247 103.385247 0 0 1-33.629762-33.695446 26.522848 26.522848 0 0 1 44.861577-28.309429 63.449904 63.449904 0 0 0 17.734446 19.704939 26.273252 26.273252 0 0 1 7.881975 36.388454 24.434124 24.434124 0 0 1-21.4127 10.969083z m-8.538807-16.223733a22.594997 22.594997 0 0 0 8.538807 3.087107 11.494548 11.494548 0 0 0 10.312251-4.926235 13.136626 13.136626 0 0 0-3.809621-18.325593l-0.591149-0.394099a77.046312 77.046312 0 0 1-21.4127-23.580244 13.405927 13.405927 0 1 0-22.200898 15.041437 89.132008 89.132008 0 0 0 29.16331 29.097627z m-32.841565-82.300962h-5.254651a26.273252 26.273252 0 0 1-20.887235-29.426043 100.232457 100.232457 0 0 1 16.749198-43.679281 24.76254 24.76254 0 0 1 16.486466-11.625914 28.11238 28.11238 0 0 1 19.704939 3.743938 25.156639 25.156639 0 0 1 11.95433 16.683515 28.11238 28.11238 0 0 1-3.743939 19.704939l-0.394099 0.591148a40.460808 40.460808 0 0 0-8.538807 23.645927v1.576395a26.273252 26.273252 0 0 1-26.076202 18.785376z m-3.218474-13.136627h3.218474a13.136626 13.136626 0 0 0 13.136626-8.144708 53.663117 53.663117 0 0 1 10.969083-30.608338 15.10712 15.10712 0 0 0 1.970493-10.312252 11.822963 11.822963 0 0 0-5.780115-7.947659 15.10712 15.10712 0 0 0-10.640667-2.167543 11.822963 11.822963 0 0 0-7.947659 5.780116 89.723156 89.723156 0 0 0-14.91007 38.81873 13.136626 13.136626 0 0 0 9.983835 14.581654z m76.126748-63.712636a27.258499 27.258499 0 0 1-26.273252-21.018601c-2.495959-12.545478 4.663502-28.572162 18.916742-31.396537a157.179731 157.179731 0 0 1 30.871071-4.072354h13.793457a26.273252 26.273252 0 0 1 0 52.546504h-13.662091a62.201924 62.201924 0 0 0-17.603079 1.642079l-1.247979 0.328415-1.707762 1.773445z m23.645927-43.350866a144.108788 144.108788 0 0 0-28.178063 3.809622 13.793457 13.793457 0 0 0-8.735856 15.895317 13.990507 13.990507 0 0 0 11.297498 10.312252 13.136626 13.136626 0 0 1 4.597819-1.707762 76.258114 76.258114 0 0 1 21.018602-2.036177h13.793457a13.136626 13.136626 0 0 0 0-26.273252z m334.983964 39.409878h-19.704939a26.273252 26.273252 0 0 1 0-52.546504h19.704939a30.739705 30.739705 0 0 0 10.837717-1.247979l1.90481-0.656832a26.8644 26.8644 0 0 1 31.133804 20.952919 28.11238 28.11238 0 0 1-20.887235 31.396536 93.79551 93.79551 0 0 1-22.989096 2.10186z m-19.704939-39.409878a13.136626 13.136626 0 0 0 0 26.273252h19.704939a82.235279 82.235279 0 0 0 20.099038-1.773444 15.10712 15.10712 0 0 0 10.9034-15.961001 13.727774 13.727774 0 0 0-15.369853-10.70635 41.183323 41.183323 0 0 1-15.632585 2.167543z m-76.849262 39.409878h-37.439384a26.273252 26.273252 0 0 1 0-52.546504h37.439384a26.273252 26.273252 0 1 1 0 52.546504z m-37.439384-39.409878a13.136626 13.136626 0 0 0 0 26.273252h37.439384a13.136626 13.136626 0 1 0 0-26.273252z m-74.878769 39.409878h-37.439384a26.273252 26.273252 0 0 1 0-52.546504h37.439384a26.273252 26.273252 0 0 1 0 52.546504z m-37.439384-39.409878a13.136626 13.136626 0 0 0 0 26.273252h37.439384a13.136626 13.136626 0 0 0 0-26.273252z m325.131494-1.970494a33.432713 33.432713 0 0 1-14.713021-4.597819l-1.05093-0.656831a27.652598 27.652598 0 0 1-5.25465-36.782553 62.793072 62.793072 0 0 0 10.640667-24.959589 25.747787 25.747787 0 0 1 13.136626-17.077614 23.186145 23.186145 0 0 1 18.916741-1.707762 25.81347 25.81347 0 0 1 16.486466 13.136626 23.251828 23.251828 0 0 1 1.642078 19.048108 125.717511 125.717511 0 0 1-19.704939 41.774471c-1.379346 7.093778-9.129955 11.822963-20.099038 11.822963z m-8.276074-16.092367a21.478384 21.478384 0 0 0 8.276074 2.955741 12.085696 12.085696 0 0 0 7.356511-1.773444v-2.10186l1.182296-1.313663a115.011161 115.011161 0 0 0 18.588326-39.409878v-0.788198a9.65542 9.65542 0 0 0-0.85388-8.276074 12.742527 12.742527 0 0 0-8.144709-6.568313l-1.116613-0.328416a9.918153 9.918153 0 0 0-8.276074 0.853881 12.742527 12.742527 0 0 0-6.568313 8.144708 74.550353 74.550353 0 0 1-13.136626 30.21424 14.713021 14.713021 0 0 0 2.693008 18.391276z m14.187556-90.314304a29.885824 29.885824 0 0 1-23.186145-12.80821 206.573445 206.573445 0 0 0-18.588326-22.332264 27.718281 27.718281 0 0 1-4.860551-36.454138 26.273252 26.273252 0 0 1 17.406029-9.721103 26.273252 26.273252 0 0 1 19.704939 4.466453 164.601924 164.601924 0 0 1 31.199487 35.271841 26.273252 26.273252 0 0 1-7.487877 34.943425c-2.693008 6.633996-11.100449 6.633996-14.187556 6.633996z m-24.959589-68.441821a15.10712 15.10712 0 0 0-2.430276 0 13.136626 13.136626 0 0 0-8.735856 4.597819 14.713021 14.713021 0 0 0 2.627325 18.391276l0.722514 0.591148a216.75433 216.75433 0 0 1 20.296087 24.499808 17.077614 17.077614 0 0 0 12.479795 7.225144h1.313663v-1.116613l3.218473-1.904811a13.136626 13.136626 0 0 0 5.583066-18.588325 151.071199 151.071199 0 0 0-28.243746-31.922002 12.085696 12.085696 0 0 0-6.831045-1.773444z m-69.624118 19.179474h-37.439385a26.273252 26.273252 0 0 1 0-52.546504h37.439385a26.273252 26.273252 0 0 1 0 52.546504z m-37.439385-39.409878a13.136626 13.136626 0 0 0 0 26.273252h37.439385a13.136626 13.136626 0 0 0 0-26.273252z m-74.878768 39.409878h-37.439384a26.273252 26.273252 0 0 1 0-52.546504h37.439384a25.156639 25.156639 0 0 1 26.273252 26.273252 26.273252 26.273252 0 0 1-26.273252 26.273252z m-37.439384-39.409878a13.136626 13.136626 0 0 0 0 26.273252h37.439384a13.136626 13.136626 0 0 0 13.136626-13.136626 12.085696 12.085696 0 0 0-13.136626-13.136626z m-76.849263 39.409878h-19.704939a26.273252 26.273252 0 0 1 0-52.546504h19.704939a26.273252 26.273252 0 1 1 0 52.546504z m-19.704939-39.409878a13.136626 13.136626 0 0 0 0 26.273252h19.704939a13.136626 13.136626 0 1 0 0-26.273252z" p-id="11199" data-spm-anchor-id="a313x.search_index.0.i14.1cc13a81cLJZuJ" class="selected" fill="#ffffff"></path>
          <path d="M222.994227 9.85247a179.117896 179.117896 0 0 0-179.314945 179.314945c0 99.181527 179.314945 342.209108 179.314945 342.209108s179.314945-243.027582 179.314946-342.209108a179.117896 179.117896 0 0 0-179.314946-179.314945z m-147.130212 179.314945a146.47338 146.47338 0 0 1 292.946761 0c0 56.487492-81.447081 192.451572-146.47338 286.378448-64.369468-93.926876-146.47338-229.890956-146.473381-286.378448z m147.130212-81.447081a81.460218 81.460218 0 1 0-2.627325 162.894162h2.627325a81.447081 81.447081 0 0 0 0-162.894162z m0 130.709429a48.605516 48.605516 0 1 1 48.605516-48.605517 48.47415 48.47415 0 0 1-48.605516 48.605517z m635.155869 169.462476a179.117896 179.117896 0 0 0-179.314945 179.314945c0 99.181527 179.314945 342.209108 179.314945 342.209108s179.314945-243.027582 179.314946-342.209108a179.117896 179.117896 0 0 0-179.314946-179.314945z m-146.47338 179.314945a146.47338 146.47338 0 1 1 292.946761 0c0 56.487492-81.447081 192.451572-146.473381 286.378448-65.026299-93.926876-146.47338-229.890956-146.47338-286.378448z m146.47338-81.447081a81.460218 81.460218 0 1 0-2.627325 162.894163h2.627325a81.447081 81.447081 0 0 0 0-162.894163z m0 130.052597a48.605516 48.605516 0 1 1 48.605517-48.605516 48.47415 48.47415 0 0 1-48.605517 48.605516z" fill="#ffffff" p-id="11200" data-spm-anchor-id="a313x.search_index.0.i11.1cc13a81cLJZuJ" class="selected"></path><path d="M858.150096 946.034124l-7.947659-10.772033c-7.356511-10.049519-181.219756-246.77152-181.219756-348.12059a189.167415 189.167415 0 1 1 378.33483 0c0 101.283387-173.863246 338.005388-181.219756 348.12059z m0-528.289416a169.593842 169.593842 0 0 0-169.462476 169.462476c0 83.154843 134.978833 277.182809 169.462476 325.394227 34.483643-48.211418 169.462476-242.239384 169.462476-325.394227a169.593842 169.593842 0 0 0-169.462476-169.462476z m0 472.918538l-8.079025-11.691598c-24.76254-35.731623-148.246825-217.870943-148.246825-291.961513a156.32585 156.32585 0 1 1 312.6517 0c0 74.156254-123.484285 256.164208-148.246825 291.961513z m0-440.076973a136.752277 136.752277 0 0 0-136.620911 136.620911c0 47.685953 67.325208 167.294933 136.620911 269.300834 69.295702-101.677486 136.620911-221.286466 136.620911-269.300834a136.752277 136.752277 0 0 0-136.620911-136.620911z m-2.627325 227.920462a91.312688 91.312688 0 1 1 2.758691-182.599102 91.299551 91.299551 0 0 1 0 182.599102z m1.445029-162.894163a71.594612 71.594612 0 0 0-1.313663 143.189224h2.495959a70.937781 70.937781 0 0 0 50.247595-21.60975 71.791661 71.791661 0 0 0-50.378961-121.579474z m1.182296 130.052598a58.457986 58.457986 0 1 1 58.457986-58.457986 58.523669 58.523669 0 0 1-58.457986 58.457986z m0-97.211033a38.753047 38.753047 0 1 0 38.753047 38.753047 38.81873 38.81873 0 0 0-38.753047-38.753047z m-635.155869-0.459782l-7.947659-10.772033C207.690058 527.435536 33.826812 290.450802 33.826812 189.167415a189.167415 189.167415 0 0 1 378.33483 0c0 101.283387-173.863246 338.005388-181.219756 348.12059z m0-528.289416a169.593842 169.593842 0 0 0-169.462476 169.462476c0 83.154843 134.978833 277.182809 169.462476 325.394227C257.47787 466.350225 392.456703 272.322258 392.456703 189.167415A169.593842 169.593842 0 0 0 222.994227 19.704939z m-0.722514 472.918538l-8.079025-11.166133c-34.549326-51.101475-148.181142-222.074663-148.181142-292.289929a156.32585 156.32585 0 0 1 312.6517 0c0 74.156254-123.484285 256.164208-148.246825 291.961514z m0-440.076973a136.752277 136.752277 0 0 0-136.555228 136.620911c0 49.328031 69.164336 169.002694 136.686594 268.906735C291.633098 356.659397 358.958307 236.853368 358.958307 189.167415A136.752277 136.752277 0 0 0 222.337396 52.546504z m-1.970494 227.920462a91.312688 91.312688 0 1 1 2.758691-182.599102 91.299551 91.299551 0 0 1 0 182.599102z m1.445029-162.894163a71.594612 71.594612 0 0 0-1.313663 143.189224h2.495959a71.594612 71.594612 0 0 0 0-143.189224h-1.05093z m1.182296 130.709429a58.457986 58.457986 0 1 1 58.457986-58.457986 58.523669 58.523669 0 0 1-58.392303 58.457986z m0-97.211033a38.753047 38.753047 0 0 0 0 77.506094 39.409878 39.409878 0 0 0 38.753047-38.753047 38.81873 38.81873 0 0 0-38.687364-38.753047z" p-id="11201" data-spm-anchor-id="a313x.search_index.0.i12.1cc13a81cLJZuJ" class="selected" fill="#ffffff"></path>
        </svg>
      </button>
      <button
        @click="PanelChange(3)"
        :class="[
          'w-8 h-8 flex items-center justify-center hover:bg-slate-700/90 cursor-pointer',
          rightPanelCollapsed ? 'right-2' : 'left-2',
        ]"
      >
        <svg
          class="w-4 h-4 transition-transform duration-200"
          :class="{ 'rotate-180': !rightPanelCollapsed }"
          fill="currentColor"
          viewBox="0 0 24 24"
        >
          <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z" />
        </svg>
      </button>
      <button
        @click="PanelChange(4)"
        :class="[
          'w-8 h-8 flex items-center justify-center hover:bg-slate-700/90 cursor-pointer',
          rightPanelCollapsed ? 'right-2' : 'left-2',
        ]"
      >
        <svg
          t="1753341000000"
          class="icon w-6 h-6"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="12000"
        >
          <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z" fill="#ffffff" p-id="12001"></path>
          <path d="M464 336a48 48 0 0 1 96 0v112h112a48 48 0 0 1 0 96H560v112a48 48 0 0 1-96 0V544H352a48 48 0 0 1 0-96h112V336z" fill="#ffffff" p-id="12002"></path>
        </svg>
      </button>
    </div>

    <!-- Panel Content -->
    <div v-show="!rightPanelCollapsed" class="h-full relative">

      <!-- 设备管理面板 -->
      <div
        v-show="currentPanel === 1"
        ref="scrollContentRef"
        class="h-full overflow-y-auto relative z-10 scrollbar-hide"
        @scroll="handleScroll"
      >
        <div class="space-y-4 p-4">
          <!-- 设备管理标题 -->
          <div class="txt_title flex items-center justify-between">
            <h2 class="font-semibold">设备管理</h2>
          </div>

          <!-- 添加设备按钮 -->
          <div class="card_box relative overflow-hidden group bg-gradient-to-br from-slate-800/80 to-slate-700/60 w-full">
            <div class="relative z-10">
              <div class="p-4">
                <button
                  @click="openAddDeviceModal"
                  class="w-full px-4 py-3 text-sm font-medium rounded border transition-all duration-200 bg-gradient-to-r from-emerald-500/20 to-emerald-500/20 border-emerald-400 text-emerald-300 hover:from-emerald-500/30 hover:to-emerald-500/30 hover:shadow-lg hover:shadow-emerald-500/20"
                >
                  ➕ 添加设备
                </button>
              </div>
            </div>
          </div>

          <!-- 设备列表 -->
          <div class="card_box relative overflow-hidden group bg-gradient-to-br from-slate-800/80 to-slate-700/60 w-full">
            <div class="relative z-10">
              <div class="txt_title flex items-center justify-between">
                <h3 class="font-semibold">设备列表 ({{ props.models.length }})</h3>
              </div>
              <div class="p-4 space-y-2 max-h-96 overflow-y-auto">
                <div
                  v-for="model in props.models"
                  :key="model.id"
                  class="flex items-center gap-2 p-3 rounded border border-slate-600/50 hover:border-slate-500 transition-colors duration-200"
                >
                  <div class="flex-1">
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-white font-medium">{{ model.name }}</span>
                      <div class="flex items-center gap-2">
                        <span
                          class="text-xs px-2 py-1 rounded"
                          :class="{
                            'bg-green-500/20 text-green-400': getModelStatus(model).color === 'green',
                            'bg-yellow-500/20 text-yellow-400': getModelStatus(model).color === 'yellow',
                            'bg-red-500/20 text-red-400': getModelStatus(model).color === 'red',
                            'bg-orange-500/20 text-orange-400': getModelStatus(model).color === 'orange'
                          }"
                        >
                          {{ getModelStatus(model).status }}
                        </span>
                      </div>
                    </div>
                    <div class="text-xs text-slate-400 mt-1">
                      ID: {{ model.id }} | 电量: {{ model.battery || 100 }}%
                    </div>
                    <div class="text-xs text-slate-500">
                      位置: ({{ model.currentPosition?.x?.toFixed(1) || 0 }}, {{ model.currentPosition?.z?.toFixed(1) || 0 }})
                    </div>
                  </div>
                  <div class="flex flex-col gap-1">
                    <button
                      @click="startEdit(model)"
                      class="text-xs text-blue-400 hover:text-blue-300 transition-colors duration-200 px-2 py-1 rounded border border-blue-500/30 hover:bg-blue-500/20"
                      title="编辑设备"
                    >
                      编辑
                    </button>
                    <button
                      @click="openSensorConfig(model)"
                      class="text-xs text-purple-400 hover:text-purple-300 transition-colors duration-200 px-2 py-1 rounded border border-purple-500/30 hover:bg-purple-500/20"
                      title="传感器配置"
                    >
                      传感器
                    </button>
                    <button
                      @click="toggleModelInfoCard(model)"
                      class="text-xs transition-colors duration-200 px-2 py-1 rounded border"
                      :class="model.showInfoCard
                        ? 'text-cyan-400 border-cyan-500/30 hover:bg-cyan-500/20'
                        : 'text-slate-400 border-slate-500/30 hover:bg-slate-500/20'"
                      title="切换信息卡片"
                    >
                      {{ model.showInfoCard ? '隐藏卡片' : '显示卡片' }}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 信息卡片全局开关 -->
          <div class="card_box relative overflow-hidden group bg-gradient-to-br from-slate-800/80 to-slate-700/60 w-full">
            <div class="relative z-10">
              <div class="p-4">
                <div class="flex items-center justify-between">
                  <div class="flex items-center gap-3">
                    <h3 class="text-sm font-semibold text-white">信息卡片全局开关</h3>
                  </div>
                  <button
                    @click="toggleInfoCards"
                    :class="[
                      'relative w-12 h-6 rounded-full transition-all duration-300',
                      showInfoCards ? 'bg-cyan-500/30 border border-cyan-400' : 'bg-slate-600/60 border border-slate-500',
                    ]"
                  >
                    <div
                      :class="[
                        'absolute top-0.5 w-5 h-5 rounded-full transition-all duration-300 shadow-lg',
                        showInfoCards
                          ? 'left-6 bg-cyan-400 shadow-cyan-400/50'
                          : 'left-0.5 bg-slate-400 shadow-slate-400/30',
                      ]"
                    ></div>
                  </button>
                </div>
                <div class="text-xs text-slate-400 mt-2">
                  {{ showInfoCards ? '显示模型信息卡片' : '隐藏模型信息卡片' }}
                </div>
                <div class="text-xs text-cyan-400">卡片会像向日葵一样始终朝向相机位置</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div
        v-show="currentPanel === 0"
        ref="scrollContentRef"
        class="h-full overflow-y-auto relative z-10 scrollbar-hide"
      >
        <div class="">
          <!-- 移动控制 -->
          <MoveControl
            :models="props.models"
            :sceneManagerRef="props.sceneManagerRef"
            @model-selected="handleModelSelected"
            @movement-started="handleMovementStarted"
            @movement-stopped="handleMovementStopped"
          />

          <!-- 仿真控制卡片 -->
          <div
            class="card_box relative overflow-hidden group bg-gradient-to-br from-slate-800/80 to-slate-700/60 w-full"
          >
            <!-- Card Glow Effect -->
            <div
              class="absolute inset-0 bg-gradient-to-r from-cyan-500/5 via-transparent to-cyan-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
            ></div>
            <div
              class="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-cyan-400/50 to-transparent"
            ></div>

            <div class="relative z-10">
              <div class="txt_title flex items-center justify-between">
                <h2 class="font-semibold">仿真控制</h2>
              </div>
              <div class="p-4">
                <div class="space-y-3">
                  <!-- 起点终点显示 -->
                  <div class="space-y-2">
                    <div class="flex items-center justify-between">
                      <div class="text-xs text-slate-400">
                        起点:
                        <span v-if="mapInteraction.startPoint" class="text-green-400">
                          🏳️ ({{ mapInteraction.startPoint.x.toFixed(1) }}, {{ mapInteraction.startPoint.z.toFixed(1) }})
                        </span>
                        <span v-else class="text-slate-500">未设置</span>
                      </div>
                      <button
                        v-if="mapInteraction.startPoint"
                        @click="clearStartPoint"
                        class="text-xs text-red-400 hover:text-red-300 transition-colors duration-200"
                        title="清除起点"
                      >
                        ✕
                      </button>
                    </div>
                    <div class="flex items-center justify-between">
                      <div class="text-xs text-slate-400">
                        终点:
                        <span v-if="mapInteraction.endPoint" class="text-red-400">
                          🏁 ({{ mapInteraction.endPoint.x.toFixed(1) }}, {{ mapInteraction.endPoint.z.toFixed(1) }})
                        </span>
                        <span v-else class="text-slate-500">未设置</span>
                      </div>
                      <button
                        v-if="mapInteraction.endPoint"
                        @click="clearEndPoint"
                        class="text-xs text-red-400 hover:text-red-300 transition-colors duration-200"
                        title="清除终点"
                      >
                        ✕
                      </button>
                    </div>
                  </div>

                  <!-- 提示信息 -->
                  <div class="text-xs text-slate-500 bg-slate-700/50 rounded p-2">
                    💡 右击地图设置起点和终点
                  </div>

                  <!-- 车型选择 -->
                  <div class="space-y-2">
                    <label class="block text-xs font-medium text-slate-400 uppercase tracking-wider">目标车型</label>
                    <select
                      v-model="selectedVehicleType"
                      @change="handleVehicleTypeChange"
                      class="w-full px-3 py-2 text-sm bg-slate-700/60 border border-cyan-500/30 rounded text-white focus:outline-none focus:border-cyan-400 focus:ring-1 focus:ring-cyan-400/50 transition-all duration-200 hover:border-cyan-400/50"
                    >
                      <option
                        v-for="option in vehicleTypeOptions"
                        :key="option.value"
                        :value="option.value"
                        class="bg-slate-800 text-white"
                      >
                        {{ option.label }}
                      </option>
                    </select>
                  </div>

                  <div class="buttons flex">
                    <!-- 能耗仿真按钮 -->
                    <button
                      @click="handleEnergySimulation"
                      :disabled="!mapInteraction.endPoint || mapInteraction.isSimulating"
                      :class="[
                        'w-full px-4 py-3 text-sm font-medium rounded border transition-all duration-200 mr-2',
                        mapInteraction.isSimulating
                          ? 'bg-blue-500/20 border-blue-400 text-blue-300 cursor-wait'
                          : mapInteraction.endPoint
                          ? 'bg-gradient-to-r from-orange-500/20 to-red-500/20 border-orange-400 text-orange-300 hover:from-orange-500/30 hover:to-red-500/30 hover:shadow-lg hover:shadow-orange-500/20'
                          : 'bg-slate-600/60 border-slate-500 text-slate-400 cursor-not-allowed'
                      ]"
                    >
                      <span v-if="mapInteraction.isSimulating">🔄 仿真中...</span>
                      <span v-else>⚡ 能耗仿真</span>
                    </button>

                    <!-- 感知模拟按钮 -->
                    <button
                      @click="handlePerceptionSimulation"
                      :disabled="isPerceptionSimulating"
                      :class="[
                        'w-full px-4 py-3 text-sm font-medium rounded border transition-all duration-200',
                        isPerceptionSimulating
                          ? 'bg-blue-500/20 border-blue-400 text-blue-300 cursor-wait'
                          : 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 border-purple-400 text-purple-300 hover:from-purple-500/30 hover:to-pink-500/30 hover:shadow-lg hover:shadow-purple-500/20'
                      ]"
                    >
                      <span v-if="isPerceptionSimulating">🔄 感知中...</span>
                      <span v-else>👁️ 感知模拟</span>
                    </button>
                  </div>

                  <!-- 设备统计 -->
                  <div class="text-xs text-slate-400">
                    场景设备: {{ props.models.length }} 台
                  </div>

                  <!-- 选中车辆电量消耗显示 -->
                  <div v-if="selectedVehicleConsumption" class="mt-3 p-3 bg-slate-700/50 rounded border border-orange-500/30">
                    <div class="flex items-center gap-2 mb-2">
                      <div class="w-2 h-2 rounded-full bg-orange-500 shadow-lg shadow-orange-500/50"></div>
                      <span class="text-xs font-medium text-orange-300">选中车辆消耗</span>
                    </div>

                    <div class="space-y-2">
                      <!-- 车辆ID -->
                      <div class="flex justify-between items-center">
                        <span class="text-xs text-slate-400">车辆ID:</span>
                        <span class="text-xs text-white font-mono">{{ selectedVehicleConsumption.deviceId }}</span>
                      </div>

                      <!-- 当前电量 -->
<!--                      <div class="flex justify-between items-center">-->
<!--                        <span class="text-xs text-slate-400">当前电量:</span>-->
<!--                        <span class="text-xs font-mono" :class="getBatteryColorClass(selectedVehicleConsumption.currentPower)">-->
<!--                          {{ selectedVehicleConsumption.currentPower.toFixed(1) }}%-->
<!--                        </span>-->
<!--                      </div>-->

                      <!-- 累计消耗 -->
                      <div class="flex justify-between items-center">
                        <span class="text-xs text-slate-400">累计消耗:</span>
                        <span class="text-xs text-orange-300 font-mono">
                          {{ selectedVehicleConsumption.totalConsume.toFixed(2) }} kWh
                        </span>
                      </div>

                      <!-- 实时消耗率 -->
                      <div class="flex justify-between items-center">
                        <span class="text-xs text-slate-400">消耗率:</span>
                        <span class="text-xs text-red-300 font-mono">
                          {{ selectedVehicleConsumption.consumeRate.toFixed(3) }} kWh/s
                        </span>
                      </div>

                      <!-- 进度条 -->
<!--                      <div class="mt-2">-->
<!--                        <div class="flex justify-between text-xs text-slate-400 mb-1">-->
<!--                          <span>电量</span>-->
<!--                          <span>{{ selectedVehicleConsumption.currentPower.toFixed(1) }}%</span>-->
<!--                        </div>-->
<!--                        <div class="w-full bg-slate-600 rounded-full h-2">-->
<!--                          <div-->
<!--                            class="h-2 rounded-full transition-all duration-300"-->
<!--                            :class="getBatteryProgressClass(selectedVehicleConsumption.currentPower)"-->
<!--                            :style="{ width: selectedVehicleConsumption.currentPower + '%' }"-->
<!--                          ></div>-->
<!--                        </div>-->
<!--                      </div>-->
                    </div>
                  </div>

                  <!-- 无选中车辆提示 -->
                  <div v-else class="mt-3 p-3 bg-slate-700/30 rounded border border-slate-600/50">
                    <div class="text-xs text-slate-500 text-center">
                      💡 执行能耗仿真后显示车辆消耗信息
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 路线模拟控制 -->
          <LineControl
            :model="props.model"
            :model-component="props.modelComponent"
            :models="props.models"
            :scene-manager="props.sceneManagerRef"
            @status-change="handleRouteStatusChange"
            @position-update="handleRoutePositionUpdate"
          />
        </div>
      </div>
      <div v-show="currentPanel === 3">
        <!-- 配置管理区域 -->
        <div
          class="card_box relative overflow-hidden group bg-gradient-to-br from-slate-800/80 to-slate-700/60 w-full"
        >
          <!-- Card Glow Effect -->
          <div
            class="absolute inset-0 bg-gradient-to-br from-cyan-400/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-md"
          ></div>
          <div class="relative z-10">
            <div class="txt_title flex items-center justify-between">
                <h2 class="font-semibold">配置管理</h2>
              </div>
            <div class="flex items-center gap-3 p-1 pb-2 border-b border-cyan-500/10">
              <div class="ml-auto text-xs text-cyan-400">{{ props.models.length }} 个设备</div>
            </div>
            <div class="p-4 space-y-3">
              <!-- 导入配置按钮 -->
              <label
                for="config-import-file"
                class="action-btn-left btn-3d w-full h-10 relative overflow-hidden group bg-gradient-to-br from-cyan-700/70 to-cyan-600/50 border border-cyan-500/40 text-cyan-200 font-medium hover:from-cyan-600/80 hover:to-cyan-500/60 hover:border-cyan-400/60 active:from-cyan-800/90 active:to-cyan-700/70 active:border-cyan-600/80 transition-all duration-150 shadow-lg shadow-cyan-500/20 hover:shadow-xl hover:shadow-cyan-500/30 active:shadow-md active:shadow-cyan-500/40 cursor-pointer flex items-center justify-center"
              >
                <input
                  type="file"
                  id="config-import-file"
                  accept=".json"
                  @change="importSceneConfig"
                  class="hidden"
                />
                <!-- 3D Effects -->
                <div
                  class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-cyan-300/40 to-transparent"
                ></div>
                <div
                  class="absolute top-0 left-0 bottom-0 w-px bg-gradient-to-b from-cyan-300/40 via-cyan-300/20 to-transparent"
                ></div>
                <div
                  class="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-cyan-900/60 to-transparent"
                ></div>
                <div
                  class="absolute top-0 right-0 bottom-0 w-px bg-gradient-to-b from-transparent via-cyan-900/40 to-cyan-900/60"
                ></div>
                <!-- Icon -->
                <div
                  class="absolute left-3 top-1/2 transform -translate-y-1/2 transition-transform duration-150 group-active:translate-y-0.5"
                >
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                    <path
                      d="M14 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 18H6V4h8v16z"
                    />
                    <path d="M8 14h8v2H8v-2zm0-3h4v2H8v-2zm0-3h8v2H8v-2z" />
                  </svg>
                </div>
                <span
                  class="relative z-10 pl-6 text-sm transition-transform duration-150 group-active:translate-y-0.5"
                  >导入配置</span
                >
                <!-- Button Glow Effect -->
                <div
                  class="absolute inset-0 bg-gradient-to-br from-cyan-400/15 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"
                ></div>
                <!-- Corner Accents -->
                <div
                  class="absolute top-1 left-1 w-2 h-2 border-l border-t border-cyan-400/60 transition-colors duration-150 group-active:border-cyan-300/80"
                ></div>
                <div
                  class="absolute bottom-1 right-1 w-2 h-2 border-r border-b border-cyan-400/60 transition-colors duration-150 group-active:border-cyan-500/80"
                ></div>
              </label>
              <!-- 导出配置按钮 -->
              <button
                @click="exportSceneConfig"
                :disabled="props.models.length === 0"
                class="action-btn-left btn-3d w-full h-10 relative overflow-hidden group bg-gradient-to-br from-orange-700/70 to-orange-600/50 border border-orange-500/40 text-orange-200 font-medium hover:from-orange-600/80 hover:to-orange-500/60 hover:border-orange-400/60 active:from-orange-800/90 active:to-orange-700/70 active:border-orange-600/80 transition-all duration-150 shadow-lg shadow-orange-500/20 hover:shadow-xl hover:shadow-orange-500/30 active:shadow-md active:shadow-orange-500/40 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <!-- 3D Effects -->
                <div
                  class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-orange-300/40 to-transparent"
                ></div>
                <div
                  class="absolute top-0 left-0 bottom-0 w-px bg-gradient-to-b from-orange-300/40 via-orange-300/20 to-transparent"
                ></div>
                <div
                  class="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-orange-900/60 to-transparent"
                ></div>
                <div
                  class="absolute top-0 right-0 bottom-0 w-px bg-gradient-to-b from-transparent via-orange-900/40 to-orange-900/60"
                ></div>
                <!-- Icon -->
                <div
                  class="absolute left-3 top-1/2 transform -translate-y-1/2 transition-transform duration-150 group-active:translate-y-0.5"
                >
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                    <path
                      d="M14 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 18H6V4h8v16z"
                    />
                    <path d="M8 14h8v2H8v-2zm0-3h4v2H8v-2zm0-3h8v2H8v-2z" />
                  </svg>
                </div>
                <span
                  class="relative z-10 pl-6 text-sm transition-transform duration-150 group-active:translate-y-0.5"
                  >导出配置</span
                >
                <!-- Button Glow Effect -->
                <div
                  class="absolute inset-0 bg-gradient-to-br from-orange-400/15 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"
                ></div>
                <!-- Corner Accents -->
                <div
                  class="absolute top-1 left-1 w-2 h-2 border-l border-t border-orange-400/60 transition-colors duration-150 group-active:border-orange-300/80"
                ></div>
                <div
                  class="absolute bottom-1 right-1 w-2 h-2 border-r border-b border-orange-400/60 transition-colors duration-150 group-active:border-orange-500/80"
                ></div>
              </button>
              <!-- 清空所有设备按钮 -->
              <button
                @click="clearAllDevices"
                :disabled="props.models.length === 0"
                class="action-btn-left btn-3d w-full h-10 relative overflow-hidden group bg-gradient-to-br from-red-700/70 to-red-600/50 border border-red-500/40 text-red-200 font-medium hover:from-red-600/80 hover:to-red-500/60 hover:border-red-400/60 active:from-red-800/90 active:to-red-700/70 active:border-red-600/80 transition-all duration-150 shadow-lg shadow-red-500/20 hover:shadow-xl hover:shadow-red-500/30 active:shadow-md active:shadow-red-500/40 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <!-- 3D Effects -->
                <div
                  class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-red-300/40 to-transparent"
                ></div>
                <div
                  class="absolute top-0 left-0 bottom-0 w-px bg-gradient-to-b from-red-300/40 via-red-300/20 to-transparent"
                ></div>
                <div
                  class="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-red-900/60 to-transparent"
                ></div>
                <div
                  class="absolute top-0 right-0 bottom-0 w-px bg-gradient-to-b from-transparent via-red-900/40 to-red-900/60"
                ></div>
                <!-- Icon -->
                <div
                  class="absolute left-3 top-1/2 transform -translate-y-1/2 transition-transform duration-150 group-active:translate-y-0.5"
                >
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M6 18L18 6M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                  </svg>
                </div>
                <span
                  class="relative z-10 pl-6 text-sm transition-transform duration-150 group-active:translate-y-0.5"
                  >清空设备</span
                >
                <!-- Button Glow Effect -->
                <div
                  class="absolute inset-0 bg-gradient-to-br from-red-400/15 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"
                ></div>
                <!-- Corner Accents -->
                <div
                  class="absolute top-1 left-1 w-2 h-2 border-l border-t border-red-400/60 transition-colors duration-150 group-active:border-red-300/80"
                ></div>
                <div
                  class="absolute bottom-1 right-1 w-2 h-2 border-r border-b border-red-400/60 transition-colors duration-150 group-active:border-red-500/80"
                ></div>
              </button>
              <!-- Add New Device Button -->
              <button
                @click="openAddDeviceModal"
                class="action-btn-left btn-3d w-full h-10 relative overflow-hidden group bg-gradient-to-br from-emerald-700/70 to-emerald-600/50 border border-emerald-500/40 text-emerald-200 font-medium hover:from-emerald-600/80 hover:to-emerald-500/60 hover:border-emerald-400/60 active:from-emerald-800/90 active:to-emerald-700/70 active:border-emerald-600/80 transition-all duration-150 shadow-lg shadow-emerald-500/20 hover:shadow-xl hover:shadow-emerald-500/30 active:shadow-md active:shadow-emerald-500/40"
              >
                <!-- 3D Top Highlight -->
                <div
                  class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-emerald-300/40 to-transparent"
                ></div>
                <!-- 3D Left Highlight -->
                <div
                  class="absolute top-0 left-0 bottom-0 w-px bg-gradient-to-b from-emerald-300/40 via-emerald-300/20 to-transparent"
                ></div>
                <!-- 3D Bottom Shadow -->
                <div
                  class="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-emerald-900/60 to-transparent"
                ></div>
                <!-- 3D Right Shadow -->
                <div
                  class="absolute top-0 right-0 bottom-0 w-px bg-gradient-to-b from-transparent via-emerald-900/40 to-emerald-900/60"
                ></div>

                <!-- Icon -->
                <div
                  class="absolute left-3 top-1/2 transform -translate-y-1/2 transition-transform duration-150 group-active:translate-y-0.5"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                    ></path>
                  </svg>
                </div>
                <span class="relative z-10 pl-6 text-sm transition-transform duration-150 group-active:translate-y-0.5"
                  >添加设备</span
                >
                <!-- Button Glow Effect -->
                <div
                  class="absolute inset-0 bg-gradient-to-br from-emerald-400/15 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"
                ></div>
                <!-- Corner Accents -->
                <div
                  class="absolute top-1 left-1 w-2 h-2 border-l border-t border-emerald-400/60 transition-colors duration-150 group-active:border-emerald-300/80"
                ></div>
                <div
                  class="absolute bottom-1 right-1 w-2 h-2 border-r border-b border-emerald-400/60 transition-colors duration-150 group-active:border-emerald-500/80"
                ></div>
              </button>
            </div>
          </div>
        </div>

        <!-- Model Cards -->
        <div
          v-for="model in props.models"
          :key="model.id"
          class="futuristic-card relative overflow-hidden group bg-gradient-to-br from-slate-800/80 to-slate-700/60 border border-cyan-500/30 shadow-lg shadow-cyan-500/10 hover:shadow-cyan-500/20 transition-all duration-300"
        >
          <!-- Card Glow Effect -->
          <div
            class="absolute inset-0 bg-gradient-to-br from-cyan-400/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-md"
          ></div>
          <!-- Corner Decorations -->
          <div class="absolute top-2 left-2 w-4 h-4 border-l-2 border-t-2 border-cyan-400/60"></div>
          <div class="absolute top-2 right-2 w-4 h-4 border-r-2 border-t-2 border-cyan-400/60"></div>
          <div class="absolute bottom-2 left-2 w-4 h-4 border-l-2 border-b-2 border-cyan-400/60"></div>
          <div class="absolute bottom-2 right-2 w-4 h-4 border-r-2 border-b-2 border-cyan-400/60"></div>
          <!-- Card Content -->
          <div class="relative z-10 p-6">
            <!-- Header Section -->
            <div class="flex justify-between items-center mb-4">
              <!-- Model ID Display -->
              <div
                class="model-id-display relative px-4 py-2 bg-gradient-to-r from-cyan-500/20 to-cyan-500/10 border border-cyan-500/50"
              >
                <span class="text-cyan-400 text-lg font-bold">{{ model.id.toUpperCase() }}</span>
              </div>
              <!-- Status Indicator -->
              <div class="flex items-center space-x-3">
                <div class="relative">
                  <!-- Status Circle -->
                  <div
                    class="w-6 h-6 rounded-full border-2 flex items-center justify-center relative"
                    :class="{
                      'border-green-500 bg-green-500/10': getModelStatus(model).color === 'green',
                      'border-red-500 bg-red-500/10': getModelStatus(model).color === 'red',
                      'border-yellow-500 bg-yellow-500/10 animate-pulse': getModelStatus(model).color === 'yellow',
                      'border-orange-500 bg-orange-500/10': getModelStatus(model).color === 'orange',
                    }"
                  >
                    <!-- Inner Dot -->
                    <div
                      class="w-2 h-2 rounded-full"
                      :class="{
                        'bg-green-500': getModelStatus(model).color === 'green',
                        'bg-red-500': getModelStatus(model).color === 'red',
                        'bg-yellow-500': getModelStatus(model).color === 'yellow',
                        'bg-orange-500': getModelStatus(model).color === 'orange',
                      }"
                    ></div>
                    <!-- Pulse Ring -->
                    <div
                      v-if="getModelStatus(model).color === 'green'"
                      class="absolute inset-0 rounded-full border-2 border-green-500 animate-ping opacity-75"
                    ></div>
                  </div>
                </div>
                <span
                  class="text-sm font-medium"
                  :class="{
                    'text-green-400': getModelStatus(model).color === 'green',
                    'text-red-400': getModelStatus(model).color === 'red',
                    'text-yellow-400': getModelStatus(model).color === 'yellow',
                    'text-orange-400': getModelStatus(model).color === 'orange',
                  }"
                >
                  {{ getModelStatus(model).status }}
                </span>
              </div>
            </div>
            <!-- Model Name with Battery -->
            <div class="mb-4">
              <div class="flex items-center justify-between mb-2">
                <h3 class="text-white text-xl font-bold">{{ model.name }}</h3>
                <!-- Battery Display -->
                <div class="flex items-center space-x-2">
                  <!-- Battery Icon -->
                  <div class="relative">
                    <!-- Battery Body -->
                    <div class="w-8 h-4 border border-slate-400 rounded-sm bg-slate-800 relative overflow-hidden">
                      <!-- Battery Fill -->
                      <div
                        class="h-full transition-all duration-300 rounded-sm"
                        :style="{ width: `${model.battery || 100}%` }"
                        :class="{
                          'bg-green-500': (model.battery || 100) >= 50,
                          'bg-yellow-500': (model.battery || 100) >= 30 && (model.battery || 100) < 50,
                          'bg-red-500': (model.battery || 100) < 30,
                        }"
                      ></div>
                      <!-- Low Battery Warning -->
                      <div
                        v-if="(model.battery || 100) < 30"
                        class="absolute inset-0 bg-red-500/20 animate-pulse"
                      ></div>
                    </div>
                    <!-- Battery Tip -->
                    <div class="absolute -right-0.5 top-1 w-1 h-2 bg-slate-400 rounded-r-sm"></div>
                  </div>
                  <!-- Battery Percentage -->
                  <span
                    class="text-xs font-mono font-semibold"
                    :class="{
                      'text-green-400': (model.battery || 100) >= 50,
                      'text-yellow-400': (model.battery || 100) >= 30 && (model.battery || 100) < 50,
                      'text-red-400': (model.battery || 100) < 30,
                    }"
                  >
                    {{ model.battery || 100 }}%
                  </span>
                </div>
              </div>
              <div class="w-12 h-0.5 bg-gradient-to-r from-cyan-400 to-transparent"></div>
            </div>
            <!-- Stats Section / Edit Form -->
            <div v-if="editingModelId === model.id" class="mb-6 space-y-4">
              <!-- Edit Form -->
              <div class="bg-slate-800/60 border border-cyan-500/30 p-4 rounded">
                <h4 class="text-cyan-300 text-sm font-medium mb-3">编辑模型参数</h4>
                <!-- Position Inputs -->
                <div class="grid grid-cols-2 gap-3 mb-3">
                  <div>
                    <label class="text-xs text-slate-400 block mb-1">X坐标</label>
                    <input
                      v-model.number="editForm.x"
                      type="number"
                      step="0.1"
                      class="w-full px-2 py-1 text-sm bg-slate-700 border border-slate-600 rounded text-white focus:border-cyan-400 focus:outline-none"
                    />
                  </div>
                  <div>
                    <label class="text-xs text-slate-400 block mb-1">Z坐标</label>
                    <input
                      v-model.number="editForm.z"
                      type="number"
                      step="0.1"
                      class="w-full px-2 py-1 text-sm bg-slate-700 border border-slate-600 rounded text-white focus:border-cyan-400 focus:outline-none"
                    />
                  </div>
                </div>
                <!-- Battery and Rotation -->
                <div class="grid grid-cols-2 gap-3 mb-4">
                  <div>
                    <label class="text-xs text-slate-400 block mb-1 flex items-center gap-1">
                      <!-- Mini Battery Icon -->
                      <div class="relative">
                        <div class="w-4 h-2 border border-slate-400 rounded-sm bg-slate-800 relative overflow-hidden">
                          <div
                            class="h-full transition-all duration-300 rounded-sm"
                            :style="{ width: `${editForm.battery}%` }"
                            :class="{
                              'bg-green-500': editForm.battery >= 50,
                              'bg-yellow-500': editForm.battery >= 30 && editForm.battery < 50,
                              'bg-red-500': editForm.battery < 30,
                            }"
                          ></div>
                        </div>
                        <div class="absolute -right-0.5 top-0.5 w-0.5 h-1 bg-slate-400 rounded-r-sm"></div>
                      </div>
                      电量 (%)
                    </label>
                    <input
                      v-model.number="editForm.battery"
                      type="number"
                      min="0"
                      max="100"
                      class="w-full px-2 py-1 text-sm bg-slate-700 border border-slate-600 rounded text-white focus:border-cyan-400 focus:outline-none"
                    />
                  </div>
                  <div>
                    <label class="text-xs text-slate-400 block mb-1">角度 (°)</label>
                    <input
                      v-model.number="editForm.rotation"
                      type="number"
                      step="1"
                      class="w-full px-2 py-1 text-sm bg-slate-700 border border-slate-600 rounded text-white focus:border-cyan-400 focus:outline-none"
                    />
                  </div>
                </div>
                <!-- Edit Buttons -->
                <div class="flex gap-2">
                  <button
                    @click="saveEdit(model)"
                    class="btn-3d flex-1 px-3 py-2 text-xs bg-gradient-to-br from-green-600 to-green-700 hover:from-green-500 hover:to-green-600 active:from-green-700 active:to-green-800 text-white rounded transition-all duration-150 font-medium shadow-lg shadow-green-500/20 hover:shadow-xl hover:shadow-green-500/30 active:shadow-md active:shadow-green-500/40 relative overflow-hidden group"
                  >
                    <!-- 3D Highlights and Shadows -->
                    <div
                      class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-green-300/40 to-transparent"
                    ></div>
                    <div
                      class="absolute top-0 left-0 bottom-0 w-px bg-gradient-to-b from-green-300/40 via-green-300/20 to-transparent"
                    ></div>
                    <div
                      class="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-green-900/60 to-transparent"
                    ></div>
                    <div
                      class="absolute top-0 right-0 bottom-0 w-px bg-gradient-to-b from-transparent via-green-900/40 to-green-900/60"
                    ></div>
                    <span class="relative z-10 transition-transform duration-150 group-active:translate-y-0.5"
                      >保存</span
                    >
                  </button>
                  <button
                    @click="cancelEdit"
                    class="btn-3d flex-1 px-3 py-2 text-xs bg-gradient-to-br from-gray-600 to-gray-700 hover:from-gray-500 hover:to-gray-600 active:from-gray-700 active:to-gray-800 text-white rounded transition-all duration-150 font-medium shadow-lg shadow-gray-500/20 hover:shadow-xl hover:shadow-gray-500/30 active:shadow-md active:shadow-gray-500/40 relative overflow-hidden group"
                  >
                    <!-- 3D Highlights and Shadows -->
                    <div
                      class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-300/40 to-transparent"
                    ></div>
                    <div
                      class="absolute top-0 left-0 bottom-0 w-px bg-gradient-to-b from-gray-300/40 via-gray-300/20 to-transparent"
                    ></div>
                    <div
                      class="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-900/60 to-transparent"
                    ></div>
                    <div
                      class="absolute top-0 right-0 bottom-0 w-px bg-gradient-to-b from-transparent via-gray-900/40 to-gray-900/60"
                    ></div>
                    <span class="relative z-10 transition-transform duration-150 group-active:translate-y-0.5"
                      >取消</span
                    >
                  </button>
                </div>
              </div>
            </div>
            <div v-else class="flex gap-3 mb-6 flex-wrap">
              <!-- Position Stat -->
              <div class="stat-item flex-1 min-w-0 relative bg-slate-700/60 border border-cyan-500/30 p-2">
                <div class="text-center">
                  <div class="text-xs text-slate-400 font-medium mb-1">POS</div>
                  <div class="text-sm text-slate-200 font-mono font-semibold">
                    {{ model.currentPosition.x.toFixed(1) }}, {{ model.currentPosition.z.toFixed(1) }}
                  </div>
                </div>
              </div>
              <!-- Rotation Stat -->
              <div class="stat-item flex-1 min-w-0 relative bg-slate-700/60 border border-cyan-500/30 p-2">
                <div class="text-center">
                  <div class="text-xs text-slate-400 font-medium mb-1">ROT</div>
                  <div class="text-sm text-slate-200 font-mono font-semibold">
                    {{ model.currentRotation.y.toFixed(0) }}°
                  </div>
                </div>
              </div>
              <!-- Sensors Stat -->
              <div class="stat-item flex-1 min-w-0 relative bg-slate-700/60 border border-cyan-500/30 p-2">
                <div class="text-center">
                  <div class="text-xs text-slate-400 font-medium mb-1">传感器</div>
                  <div class="text-sm text-slate-200 font-mono font-semibold">
                    {{ model.sensors?.length || 0 }}个
                    <span v-if="model.sensors?.some(s => s.enabled)" class="text-green-400">●</span>
                    <span v-else class="text-gray-400">○</span>
                  </div>
                </div>
              </div>
              <!-- FPV Status -->
              <div
                v-if="props.firstPersonView.targetModelId === model.id"
                class="stat-item flex-1 min-w-0 relative bg-cyan-500/20 border border-cyan-400 p-2"
              >
                <div class="text-center">
                  <div class="text-xs text-cyan-300 font-medium mb-1">👁️</div>
                  <div class="text-sm text-cyan-200 font-mono font-semibold">FPV</div>
                </div>
              </div>
            </div>
            <!-- Control Buttons -->
            <div class="space-y-3">
              <!-- First Row: FPV and Edit -->
              <div class="flex gap-3">
                <!-- First Person View Button -->
                <button
                  @click="toggleFirstPersonView(model.id)"
                  :class="[
                    'control-btn btn-3d flex-1 h-10 relative overflow-hidden group transition-all duration-150 font-medium text-sm shadow-lg hover:shadow-xl active:shadow-md',
                    props.firstPersonView.targetModelId === model.id
                      ? 'bg-gradient-to-r from-green-600/50 to-green-500/30 border border-green-500 text-green-300 hover:from-green-600/70 hover:to-green-500/50 active:from-green-700/80 active:to-green-600/60 shadow-green-500/20 hover:shadow-green-500/30 active:shadow-green-500/40'
                      : 'bg-gradient-to-r from-cyan-600/50 to-cyan-500/30 border border-cyan-500 text-cyan-300 hover:from-cyan-600/70 hover:to-cyan-500/50 active:from-cyan-700/80 active:to-cyan-600/60 shadow-cyan-500/20 hover:shadow-cyan-500/30 active:shadow-cyan-500/40',
                    { 'opacity-50 cursor-not-allowed': props.loadingStatus[model.id] !== 'loaded' },
                  ]"
                  :disabled="props.loadingStatus[model.id] !== 'loaded'"
                >
                  <!-- 3D Highlights and Shadows -->
                  <div
                    class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"
                  ></div>
                  <div
                    class="absolute top-0 left-0 bottom-0 w-px bg-gradient-to-b from-white/20 via-white/10 to-transparent"
                  ></div>
                  <div
                    class="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-black/30 to-transparent"
                  ></div>
                  <div
                    class="absolute top-0 right-0 bottom-0 w-px bg-gradient-to-b from-transparent via-black/20 to-black/30"
                  ></div>
                  <span class="relative z-10 transition-transform duration-150 group-active:translate-y-0.5">
                    {{ props.firstPersonView.targetModelId === model.id ? '退出视角' : '进入视角' }}
                  </span>
                  <!-- Button Glow -->
                  <div
                    class="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"
                    :class="
                      props.firstPersonView.targetModelId === model.id
                        ? 'bg-gradient-to-r from-green-400/30 to-transparent'
                        : 'bg-gradient-to-r from-cyan-400/30 to-transparent'
                    "
                  ></div>
                </button>
                <!-- Edit Button -->
                <button
                  @click="startEdit(model)"
                  v-if="editingModelId !== model.id"
                  class="control-btn btn-3d px-4 h-10 relative overflow-hidden group bg-gradient-to-r from-blue-600/50 to-blue-500/30 border border-blue-500 text-blue-400 hover:from-blue-600/70 hover:to-blue-500/50 active:from-blue-700/80 active:to-blue-600/60 transition-all duration-150 font-medium text-sm shadow-lg shadow-blue-500/20 hover:shadow-xl hover:shadow-blue-500/30 active:shadow-md active:shadow-blue-500/40"
                >
                  <!-- 3D Highlights and Shadows -->
                  <div
                    class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-blue-300/30 to-transparent"
                  ></div>
                  <div
                    class="absolute top-0 left-0 bottom-0 w-px bg-gradient-to-b from-blue-300/30 via-blue-300/15 to-transparent"
                  ></div>
                  <div
                    class="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-blue-900/50 to-transparent"
                  ></div>
                  <div
                    class="absolute top-0 right-0 bottom-0 w-px bg-gradient-to-b from-transparent via-blue-900/30 to-blue-900/50"
                  ></div>
                  <span class="relative z-10 transition-transform duration-150 group-active:translate-y-0.5"
                    >编辑</span
                  >
                  <!-- Button Glow -->
                  <div
                    class="absolute inset-0 bg-gradient-to-r from-blue-400/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"
                  ></div>
                </button>
              </div>
              <!-- Camera Position Settings (only show when FPV is active) -->
              <div
                v-if="props.firstPersonView.targetModelId === model.id"
                class="mt-3 p-3 bg-slate-800/60 border border-cyan-500/30 rounded"
              >
                <div class="text-xs text-cyan-400 font-medium mb-3">📷 相机位置设置</div>
                <!-- Camera Presets -->
                <div class="grid grid-cols-2 gap-2 mb-3">
                  <button
                    v-for="(preset, key) in firstPersonView.presets"
                    :key="key"
                    @click="setCameraPreset(key)"
                    :class="[
                      'px-2 py-1 text-xs rounded border transition-all duration-200',
                      firstPersonView.currentPreset === key
                        ? 'bg-cyan-500/30 border-cyan-400 text-cyan-300'
                        : 'bg-slate-700/60 border-slate-600 text-slate-300 hover:bg-slate-600/60',
                    ]"
                  >
                    {{
                      key === 'driver'
                        ? '驾驶员'
                        : key === 'overhead'
                        ? '俯视'
                        : key === 'follow'
                        ? '跟随'
                        : key === 'side'
                        ? '侧视'
                        : '自定义'
                    }}
                  </button>
                </div>
                <!-- Custom Position Controls -->
                <div v-if="firstPersonView.currentPreset === 'custom'" class="space-y-2">
                  <div class="text-xs text-slate-400 mb-2">相机位置 (模型坐标系)</div>
                  <div class="grid grid-cols-3 gap-1">
                    <input
                      v-model.number="firstPersonView.cameraOffset.x"
                      @input="updateCustomCameraPosition"
                      type="number"
                      step="0.5"
                      placeholder="X"
                      class="px-2 py-1 text-xs bg-slate-700/60 border border-slate-600 text-white rounded focus:border-cyan-400 focus:outline-none"
                    />
                    <input
                      v-model.number="firstPersonView.cameraOffset.y"
                      @input="updateCustomCameraPosition"
                      type="number"
                      step="0.5"
                      placeholder="Y"
                      class="px-2 py-1 text-xs bg-slate-700/60 border border-slate-600 text-white rounded focus:border-cyan-400 focus:outline-none"
                    />
                    <input
                      v-model.number="firstPersonView.cameraOffset.z"
                      @input="updateCustomCameraPosition"
                      type="number"
                      step="0.5"
                      placeholder="Z"
                      class="px-2 py-1 text-xs bg-slate-700/60 border border-slate-600 text-white rounded focus:border-cyan-400 focus:outline-none"
                    />
                  </div>
                  <div class="text-xs text-slate-400 mb-2">朝向目标 (模型坐标系)</div>
                  <div class="grid grid-cols-3 gap-1">
                    <input
                      v-model.number="firstPersonView.lookAtOffset.x"
                      @input="updateCustomCameraPosition"
                      type="number"
                      step="0.5"
                      placeholder="X"
                      class="px-2 py-1 text-xs bg-slate-700/60 border border-slate-600 text-white rounded focus:border-cyan-400 focus:outline-none"
                    />
                    <input
                      v-model.number="firstPersonView.lookAtOffset.y"
                      @input="updateCustomCameraPosition"
                      type="number"
                      step="0.5"
                      placeholder="Y"
                      class="px-2 py-1 text-xs bg-slate-700/60 border border-slate-600 text-white rounded focus:border-cyan-400 focus:outline-none"
                    />
                    <input
                      v-model.number="firstPersonView.lookAtOffset.z"
                      @input="updateCustomCameraPosition"
                      type="number"
                      step="0.5"
                      placeholder="Z"
                      class="px-2 py-1 text-xs bg-slate-700/60 border border-slate-600 text-white rounded focus:border-cyan-400 focus:outline-none"
                    />
                  </div>
                </div>
              </div>
              <!-- Second Row: Sensor Config and Remove -->
              <div class="flex gap-3">
                <!-- Sensor Config Button -->
                <button
                  @click="openSensorConfig(model)"
                  class="control-btn btn-3d flex-1 h-10 relative overflow-hidden group bg-gradient-to-r from-purple-600/50 to-purple-500/30 border border-purple-500 text-purple-400 hover:from-purple-600/70 hover:to-purple-500/50 active:from-purple-700/80 active:to-purple-600/60 transition-all duration-150 font-medium text-sm shadow-lg shadow-purple-500/20 hover:shadow-xl hover:shadow-purple-500/30 active:shadow-md active:shadow-purple-500/40"
                >
                  <!-- 3D Highlights and Shadows -->
                  <div
                    class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-purple-300/30 to-transparent"
                  ></div>
                  <div
                    class="absolute top-0 left-0 bottom-0 w-px bg-gradient-to-b from-purple-300/30 via-purple-300/15 to-transparent"
                  ></div>
                  <div
                    class="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-purple-900/50 to-transparent"
                  ></div>
                  <div
                    class="absolute top-0 right-0 bottom-0 w-px bg-gradient-to-b from-transparent via-purple-900/30 to-purple-900/50"
                  ></div>
                  <span class="relative z-10 transition-transform duration-150 group-active:translate-y-0.5"
                    >传感器配置</span
                  >
                  <!-- Button Glow -->
                  <div
                    class="absolute inset-0 bg-gradient-to-r from-purple-400/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"
                  ></div>
                </button>
                <!-- Remove Button -->
                <button
                  @click="removeModel(model.id)"
                  class="control-btn btn-3d px-4 h-10 relative overflow-hidden group bg-gradient-to-r from-red-600/50 to-red-500/30 border border-red-500 text-red-400 hover:from-red-600/70 hover:to-red-500/50 active:from-red-700/80 active:to-red-600/60 transition-all duration-150 font-medium text-sm shadow-lg shadow-red-500/20 hover:shadow-xl hover:shadow-red-500/30 active:shadow-md active:shadow-red-500/40"
                >
                  <!-- 3D Highlights and Shadows -->
                  <div
                    class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-red-300/30 to-transparent"
                  ></div>
                  <div
                    class="absolute top-0 left-0 bottom-0 w-px bg-gradient-to-b from-red-300/30 via-red-300/15 to-transparent"
                  ></div>
                  <div
                    class="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-red-900/50 to-transparent"
                  ></div>
                  <div
                    class="absolute top-0 right-0 bottom-0 w-px bg-gradient-to-b from-transparent via-red-900/30 to-red-900/50"
                  ></div>
                  <span class="relative z-10 transition-transform duration-150 group-active:translate-y-0.5"
                    >移除</span
                  >
                  <!-- Button Glow -->
                  <div
                    class="absolute inset-0 bg-gradient-to-r from-red-400/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"
                  ></div>
                </button>
              </div>
              <!-- Third Row: Info Card Toggle -->
              <div class="flex gap-3 mt-3">
                <!-- Info Card Toggle Button -->
                <button
                  @click="toggleModelInfoCard(model)"
                  :class="[
                    'control-btn btn-3d flex-1 h-10 relative overflow-hidden group border transition-all duration-150 font-medium text-sm shadow-lg hover:shadow-xl active:shadow-md',
                    model.showInfoCard
                      ? 'bg-gradient-to-r from-cyan-600/50 to-cyan-500/30 border-cyan-500 text-cyan-300 hover:from-cyan-600/70 hover:to-cyan-500/50 active:from-cyan-700/80 active:to-cyan-600/60 shadow-cyan-500/20 hover:shadow-cyan-500/30 active:shadow-cyan-500/40'
                      : 'bg-gradient-to-r from-slate-600/50 to-slate-500/30 border-slate-500 text-slate-400 hover:from-slate-600/70 hover:to-slate-500/50 active:from-slate-700/80 active:to-slate-600/60 shadow-slate-500/20 hover:shadow-slate-500/30 active:shadow-slate-500/40',
                  ]"
                >
                  <!-- 3D Highlights and Shadows -->
                  <div
                    :class="[
                      'absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent to-transparent',
                      model.showInfoCard ? 'via-cyan-300/30' : 'via-slate-300/30',
                    ]"
                  ></div>
                  <div
                    :class="[
                      'absolute top-0 left-0 bottom-0 w-px bg-gradient-to-b from-transparent to-transparent',
                      model.showInfoCard ? 'via-cyan-300/15' : 'via-slate-300/15',
                    ]"
                  ></div>
                  <div
                    :class="[
                      'absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent to-transparent',
                      model.showInfoCard ? 'via-cyan-900/50' : 'via-slate-900/50',
                    ]"
                  ></div>
                  <div
                    :class="[
                      'absolute top-0 right-0 bottom-0 w-px bg-gradient-to-b from-transparent to-transparent',
                      model.showInfoCard ? 'via-cyan-900/30 to-cyan-900/50' : 'via-slate-900/30 to-slate-900/50',
                    ]"
                  ></div>
                  <span
                    class="relative z-10 transition-transform duration-150 group-active:translate-y-0.5 flex items-center gap-2"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        :d="
                          model.showInfoCard
                            ? 'M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z'
                            : 'M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21'
                        "
                      ></path>
                    </svg>
                    {{ model.showInfoCard ? '隐藏信息卡片' : '显示信息卡片' }}
                  </span>
                  <!-- Button Glow -->
                  <div
                    :class="[
                      'absolute inset-0 bg-gradient-to-r from-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm',
                      model.showInfoCard ? 'from-cyan-400/30' : 'from-slate-400/30',
                    ]"
                  ></div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 地图管理和障碍物管理面板 -->
      <div
        v-show="currentPanel === 2"
        ref="scrollContentRef"
        class="h-full overflow-y-auto relative z-10 scrollbar-hide"
        @scroll="handleScroll"
      >
        <div class="space-y-4 p-4">
          <!-- 地图管理 -->
          <div class="card_box relative overflow-hidden group bg-gradient-to-br from-slate-800/80 to-slate-700/60 w-full">
            <div class="relative z-10">
              <div class="txt_title flex items-center justify-between">
                <h3 class="font-semibold">地图管理</h3>
              </div>
              <div class="p-4 space-y-3">
                <!-- 当前地图显示 -->
                <div class="text-sm text-white mb-3">
                  当前地图: {{ currentMapName || '未加载地图' }}
                </div>

                <!-- 地图选择 -->
                <div class="space-y-2">
                  <div
                    v-for="map in availableMaps"
                    :key="map.id"
                    class="flex items-center gap-3 p-3 rounded border border-slate-600/50 hover:border-slate-500 transition-colors duration-200"
                  >
                    <input
                      type="radio"
                      :id="`map-${map.id}`"
                      v-model="selectedMap"
                      :value="map"
                      @change="handleMapSelection"
                      class="w-4 h-4 text-cyan-500 bg-slate-700 border-slate-600 focus:ring-cyan-500 focus:ring-2"
                    />
                    <label :for="`map-${map.id}`" class="flex-1 cursor-pointer">
                      <div class="text-sm text-white font-medium">{{ map.name }}</div>
                      <div class="text-xs text-slate-400">ID: {{ map.id }}</div>
                      <div class="text-xs" :class="{
                        'text-green-400': getMapStatus(map) === '可用',
                        'text-red-400': getMapStatus(map) !== '可用'
                      }">
                        状态: {{ getMapStatus(map) }}
                      </div>
                    </label>
                  </div>
                </div>

                <button
                  @click="loadSelectedMap"
                  :disabled="!selectedMap || isLoadingMap"
                  class="w-full px-4 py-3 text-sm font-medium rounded border transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  :class="isLoadingMap
                    ? 'bg-yellow-500/20 border-yellow-400 text-yellow-300'
                    : 'bg-gradient-to-r from-cyan-500/20 to-cyan-500/20 border-cyan-400 text-cyan-300 hover:from-cyan-500/30 hover:to-cyan-500/30 hover:shadow-lg hover:shadow-cyan-500/20'"
                >
                  {{ isLoadingMap ? '加载中...' : '加载地图' }}
                </button>
              </div>
            </div>
          </div>

          <!-- 障碍物管理 -->
          <div class="card_box relative overflow-hidden group bg-gradient-to-br from-slate-800/80 to-slate-700/60 w-full">
            <div class="relative z-10">
              <div class="txt_title flex items-center justify-between">
                <h3 class="font-semibold">障碍物管理</h3>
              </div>
              <div class="p-4 space-y-3">
                <button
                  @click="showAddObstacleModal"
                  :disabled="isAddingObstacle"
                  class="w-full px-4 py-3 text-sm font-medium rounded border transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  :class="isAddingObstacle
                    ? 'bg-yellow-500/20 border-yellow-400 text-yellow-300'
                    : 'bg-gradient-to-r from-purple-500/20 to-purple-500/20 border-purple-400 text-purple-300 hover:from-purple-500/30 hover:to-purple-500/30 hover:shadow-lg hover:shadow-purple-500/20'"
                >
                  {{ isAddingObstacle ? '添加中...' : '➕ 添加障碍物' }}
                </button>

                <!-- 障碍物列表 -->
                <div v-if="obstacles.length > 0" class="space-y-2 max-h-48 overflow-y-auto">
                  <div
                    v-for="obstacle in obstacles"
                    :key="obstacle.id"
                    class="flex items-center justify-between p-2 rounded border border-slate-600/50"
                  >
                    <div class="flex-1">
                      <div class="text-sm text-white">{{ obstacle.name }}</div>
                      <div class="text-xs text-slate-400">
                        位置: ({{ obstacle.position.x }}, {{ obstacle.position.z }})
                      </div>
                    </div>
                    <button
                      @click="removeObstacle(obstacle.id)"
                      class="text-xs text-red-400 hover:text-red-300 transition-colors duration-200 px-2 py-1 rounded border border-red-500/30 hover:bg-red-500/20"
                      title="删除障碍物"
                    >
                      删除
                    </button>
                  </div>
                </div>
                <div v-else class="text-xs text-slate-500 text-center py-4">
                  暂无障碍物
                </div>
              </div>
            </div>
          </div>

          <!-- 路径执行控制 -->
          <div class="card_box relative overflow-hidden group bg-gradient-to-br from-slate-800/80 to-slate-700/60 w-full">
            <div class="relative z-10">
              <div class="txt_title flex items-center justify-between">
                <h3 class="font-semibold">路径执行控制</h3>
              </div>
              <div class="p-4 space-y-3">
                <div class="flex gap-2">
                  <button
                    @click="startPathExecution"
                    class="flex-1 px-4 py-2 text-sm font-medium rounded border transition-all duration-200 bg-gradient-to-r from-green-500/20 to-green-500/20 border-green-400 text-green-300 hover:from-green-500/30 hover:to-green-500/30 hover:shadow-lg hover:shadow-green-500/20"
                  >
                    ▶️ 开始执行
                  </button>
                  <button
                    @click="stopPathExecution"
                    class="flex-1 px-4 py-2 text-sm font-medium rounded border transition-all duration-200 bg-gradient-to-r from-red-500/20 to-red-500/20 border-red-400 text-red-300 hover:from-red-500/30 hover:to-red-500/30 hover:shadow-lg hover:shadow-red-500/20"
                  >
                    ⏹️ 停止执行
                  </button>
                </div>

                <!-- 速度控制 -->
                <div class="space-y-2">
                  <div class="flex items-center justify-between">
                    <span class="text-sm text-white">执行速度</span>
                    <span class="text-sm text-cyan-400">{{ speedMultiplier }}x</span>
                  </div>
                  <input
                    type="range"
                    v-model="speedMultiplier"
                    @input="updateSpeedMultiplier"
                    min="1"
                    max="100"
                    step="1"
                    class="w-full h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer slider"
                  />
                  <div class="flex gap-1">
                    <button
                      v-for="speed in [1, 5, 10, 20, 50, 100]"
                      :key="speed"
                      @click="setSpeed(speed)"
                      class="flex-1 px-2 py-1 text-xs rounded border transition-all duration-200"
                      :class="speedMultiplier === speed
                        ? 'bg-cyan-500/30 border-cyan-400 text-cyan-300'
                        : 'bg-slate-600/30 border-slate-500 text-slate-300 hover:bg-slate-500/30'"
                    >
                      {{ speed }}x
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 第一人称视角控制 -->
          <div class="card_box relative overflow-hidden group bg-gradient-to-br from-slate-800/80 to-slate-700/60 w-full">
            <div class="relative z-10">
              <div class="txt_title flex items-center justify-between">
                <h3 class="font-semibold">第一人称视角</h3>
              </div>
              <div class="p-4 space-y-3">
                <!-- 相机预设 -->
                <div class="space-y-2">
                  <div class="text-sm text-white">相机预设</div>
                  <div class="grid grid-cols-2 gap-2">
                    <button
                      v-for="(preset, name) in firstPersonView.presets"
                      :key="name"
                      @click="setCameraPreset(name)"
                      class="px-3 py-2 text-xs rounded border transition-all duration-200"
                      :class="firstPersonView.currentPreset === name
                        ? 'bg-cyan-500/30 border-cyan-400 text-cyan-300'
                        : 'bg-slate-600/30 border-slate-500 text-slate-300 hover:bg-slate-500/30'"
                    >
                      {{ name }}
                    </button>
                  </div>
                </div>

                <!-- 自定义相机位置 -->
                <div class="space-y-2">
                  <div class="text-sm text-white">自定义位置</div>
                  <div class="grid grid-cols-3 gap-2">
                    <div>
                      <label class="text-xs text-slate-400">X</label>
                      <input
                        type="number"
                        v-model="firstPersonView.cameraOffset.x"
                        @change="updateCustomCameraPosition"
                        class="w-full px-2 py-1 text-xs bg-slate-700 border border-slate-600 rounded text-white"
                      />
                    </div>
                    <div>
                      <label class="text-xs text-slate-400">Y</label>
                      <input
                        type="number"
                        v-model="firstPersonView.cameraOffset.y"
                        @change="updateCustomCameraPosition"
                        class="w-full px-2 py-1 text-xs bg-slate-700 border border-slate-600 rounded text-white"
                      />
                    </div>
                    <div>
                      <label class="text-xs text-slate-400">Z</label>
                      <input
                        type="number"
                        v-model="firstPersonView.cameraOffset.z"
                        @change="updateCustomCameraPosition"
                        class="w-full px-2 py-1 text-xs bg-slate-700 border border-slate-600 rounded text-white"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 调度请求面板 -->
      <div
        v-show="currentPanel === 4"
        ref="scrollContentRef"
        class="h-full overflow-y-auto relative z-10 scrollbar-hide"
        @scroll="handleScroll"
      >
        <div class="space-y-4 p-4">
          <ScheduleRequestForm
            ref="scheduleRequestFormRef"
            :scene-manager-ref="props.sceneManagerRef"
            @coordinate-request="handleCoordinateRequest"
            @schedule-submit="handleScheduleSubmit"
          />
        </div>
      </div>
    </div>
  </div>
  <!-- 添加设备弹框 -->
  <AddDeviceModal :visible="showAddDeviceModal" @close="closeAddDeviceModal" @confirm="confirmAddDevice" />

  <!-- 添加障碍物弹框 -->
  <AddObstacleModal
    :visible="showAddObstacleModalVisible"
    @close="showAddObstacleModalVisible = false"
    @confirm="handleAddObstacle"
  />

  <!-- 设备编辑模态框 -->
  <div v-if="editingModelId" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
    <div class="bg-slate-800 p-6 rounded-lg border border-slate-600 w-96">
      <h3 class="text-lg font-semibold text-white mb-4">编辑设备</h3>
      <div class="space-y-4">
        <div>
          <label class="block text-sm text-slate-300 mb-1">X坐标</label>
          <input
            type="number"
            v-model="editForm.x"
            class="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white"
          />
        </div>
        <div>
          <label class="block text-sm text-slate-300 mb-1">Z坐标</label>
          <input
            type="number"
            v-model="editForm.z"
            class="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white"
          />
        </div>
        <div>
          <label class="block text-sm text-slate-300 mb-1">电量</label>
          <input
            type="number"
            v-model="editForm.battery"
            min="0"
            max="100"
            class="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white"
          />
        </div>
        <div>
          <label class="block text-sm text-slate-300 mb-1">旋转角度</label>
          <input
            type="number"
            v-model="editForm.rotation"
            class="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white"
          />
        </div>
      </div>
      <div class="flex gap-3 mt-6">
        <button
          @click="saveEdit(props.models.find(m => m.id === editingModelId))"
          class="flex-1 px-4 py-2 bg-cyan-600 hover:bg-cyan-700 text-white rounded transition-colors"
        >
          保存
        </button>
        <button
          @click="cancelEdit"
          class="flex-1 px-4 py-2 bg-slate-600 hover:bg-slate-700 text-white rounded transition-colors"
        >
          取消
        </button>
      </div>
    </div>
  </div>

  <!-- 传感器配置模态框 -->
  <div v-if="showSensorConfig" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
    <div class="bg-slate-800 p-6 rounded-lg border border-slate-600 w-[600px] max-h-[80vh] overflow-y-auto">
      <h3 class="text-lg font-semibold text-white mb-4">传感器配置</h3>

      <!-- 传感器列表 -->
      <div class="mb-6">
        <div class="flex items-center justify-between mb-3">
          <h4 class="text-md font-medium text-white">传感器列表</h4>
          <button
            @click="addNewSensor(props.models.find(m => m.id === showSensorConfig))"
            class="px-3 py-1 bg-green-600 hover:bg-green-700 text-white rounded text-sm transition-colors"
          >
            添加传感器
          </button>
        </div>

        <div class="space-y-2 max-h-48 overflow-y-auto">
          <div
            v-for="(sensor, index) in (props.models.find(m => m.id === showSensorConfig)?.sensors || [])"
            :key="sensor.id"
            class="flex items-center justify-between p-3 bg-slate-700 rounded border border-slate-600"
          >
            <div class="flex-1">
              <div class="text-sm text-white font-medium">{{ sensor.name }}</div>
              <div class="text-xs text-slate-400">
                位置: ({{ sensor.relativePosition.x }}, {{ sensor.relativePosition.y }}, {{ sensor.relativePosition.z }})
              </div>
              <div class="text-xs text-slate-400">
                俯仰: {{ sensor.pitchRange.min }}° ~ {{ sensor.pitchRange.max }}°
              </div>
              <div class="text-xs text-slate-400">
                偏航: {{ sensor.yawRange.min }}° ~ {{ sensor.yawRange.max }}°
              </div>
            </div>
            <div class="flex items-center gap-2">
              <button
                @click="toggleSensorEnabled(sensor)"
                class="px-2 py-1 text-xs rounded transition-colors"
                :class="sensor.enabled
                  ? 'bg-green-600 hover:bg-green-700 text-white'
                  : 'bg-slate-600 hover:bg-slate-700 text-slate-300'"
              >
                {{ sensor.enabled ? '启用' : '禁用' }}
              </button>
              <button
                @click="editSensor(props.models.find(m => m.id === showSensorConfig), index)"
                class="px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-xs transition-colors"
              >
                编辑
              </button>
              <button
                @click="deleteSensor(props.models.find(m => m.id === showSensorConfig), index)"
                class="px-2 py-1 bg-red-600 hover:bg-red-700 text-white rounded text-xs transition-colors"
              >
                删除
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 传感器编辑表单 -->
      <div v-if="editingSensorIndex >= -1" class="mb-6 p-4 bg-slate-700 rounded border border-slate-600">
        <h4 class="text-md font-medium text-white mb-3">
          {{ editingSensorIndex === -1 ? '添加传感器' : '编辑传感器' }}
        </h4>
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-sm text-slate-300 mb-1">传感器名称</label>
            <input
              type="text"
              v-model="sensorForm.name"
              class="w-full px-3 py-2 bg-slate-600 border border-slate-500 rounded text-white"
            />
          </div>
          <div>
            <label class="block text-sm text-slate-300 mb-1">激光密度</label>
            <input
              type="number"
              v-model="sensorForm.laserDensity"
              min="1"
              max="50"
              class="w-full px-3 py-2 bg-slate-600 border border-slate-500 rounded text-white"
            />
          </div>
          <div>
            <label class="block text-sm text-slate-300 mb-1">相对位置 X</label>
            <input
              type="number"
              v-model="sensorForm.relativePosition.x"
              step="0.1"
              class="w-full px-3 py-2 bg-slate-600 border border-slate-500 rounded text-white"
            />
          </div>
          <div>
            <label class="block text-sm text-slate-300 mb-1">相对位置 Y</label>
            <input
              type="number"
              v-model="sensorForm.relativePosition.y"
              step="0.1"
              class="w-full px-3 py-2 bg-slate-600 border border-slate-500 rounded text-white"
            />
          </div>
          <div>
            <label class="block text-sm text-slate-300 mb-1">相对位置 Z</label>
            <input
              type="number"
              v-model="sensorForm.relativePosition.z"
              step="0.1"
              class="w-full px-3 py-2 bg-slate-600 border border-slate-500 rounded text-white"
            />
          </div>
          <div>
            <label class="block text-sm text-slate-300 mb-1">俯仰角最小值</label>
            <input
              type="number"
              v-model="sensorForm.pitchRange.min"
              min="-90"
              max="90"
              class="w-full px-3 py-2 bg-slate-600 border border-slate-500 rounded text-white"
            />
          </div>
          <div>
            <label class="block text-sm text-slate-300 mb-1">俯仰角最大值</label>
            <input
              type="number"
              v-model="sensorForm.pitchRange.max"
              min="-90"
              max="90"
              class="w-full px-3 py-2 bg-slate-600 border border-slate-500 rounded text-white"
            />
          </div>
          <div>
            <label class="block text-sm text-slate-300 mb-1">偏航角最小值</label>
            <input
              type="number"
              v-model="sensorForm.yawRange.min"
              min="-180"
              max="180"
              class="w-full px-3 py-2 bg-slate-600 border border-slate-500 rounded text-white"
            />
          </div>
          <div>
            <label class="block text-sm text-slate-300 mb-1">偏航角最大值</label>
            <input
              type="number"
              v-model="sensorForm.yawRange.max"
              min="-180"
              max="180"
              class="w-full px-3 py-2 bg-slate-600 border border-slate-500 rounded text-white"
            />
          </div>
        </div>
        <div class="flex gap-3 mt-4">
          <button
            @click="saveSensor(props.models.find(m => m.id === showSensorConfig))"
            class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded transition-colors"
          >
            保存传感器
          </button>
          <button
            @click="editingSensorIndex = -2"
            class="px-4 py-2 bg-slate-600 hover:bg-slate-700 text-white rounded transition-colors"
          >
            取消
          </button>
        </div>
      </div>

      <div class="flex gap-3">
        <button
          @click="closeSensorConfig"
          class="flex-1 px-4 py-2 bg-slate-600 hover:bg-slate-700 text-white rounded transition-colors"
        >
          关闭
        </button>
      </div>
    </div>
  </div>

</template>

<style scoped>
/* Hide the default scrollbar for the content div */
.scrollbar-hide::-webkit-scrollbar {
  width: 0px; /* For Chrome, Safari, and Opera */
  background: transparent; /* Optional: just make it transparent */
}

.scrollbar-hide {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

/* Symmetric futuristic card style - opposite corners cut (top-left and bottom-right) */
.futuristic-card-symmetric {
  clip-path: polygon(1.5rem 0, 100% 0, 100% calc(100% - 1.5rem), calc(100% - 1.5rem) 100%, 0 100%, 0 1.5rem);
}

/* Symmetric futuristic button style */
.futuristic-btn-symmetric {
  clip-path: polygon(0.75rem 0, 100% 0, 100% calc(100% - 0.75rem), calc(100% - 0.75rem) 100%, 0 100%, 0 0.75rem);
}

/* Custom animations that extend Tailwind's built-in ones */
@keyframes ping {
  75%,
  100% {
    transform: scale(2);
    opacity: 0;
  }
}
</style>
<style scoped>
.right_part {
  margin: 110px 15px;
}
.menu_btns{
  left: -40px;
  display: flex;
  flex-direction: column;
  gap: 0px;
}
.txt_title {
  background-image: url('../../../assets/img/text_title.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  width: 100%;
  height: 40px;
  padding-left: 60px;
  padding-right: 10px;
  border-radius: 10px 0px 0px 0px;
}
.txt_title h2 {
  font-size: 18px !important;
  font-weight: 600;
  color: #fff;
}
.card_box{
  margin-top: 10px;
  border-radius: 10px;
}
</style>
