<script setup lang="ts">
import * as THREE from 'three';
import { nextTick, onMounted, onUnmounted, ref, watch, type PropType } from 'vue';
import AddDeviceModal from './components/AddDeviceModal.vue';
import AddObstacleModal from './components/AddObstacleModal.vue';
import SceneManager from './components/SceneManager.vue';

interface LaserSensor {
  id: string;
  name: string;
  relativePosition: { x: number; y: number; z: number }; // 激光在车辆中心点的相对位置
  pitchRange: { min: number; max: number }; // 俯仰角范围
  yawRange: { min: number; max: number }; // 水平角范围
  laserDensity: number; // 激光密度 (每度激光线数量)
  enabled: boolean; // 是否启用
}

interface Model {
  id: string;
  name: string;
  path: string;
  position: { x: number; y: number; z: number };
  rotation: { x: number; y: number; z: number };
  scale: number;
  currentPosition: { x: number; y: number; z: number };
  currentRotation: { x: number; y: number; z: number };
  battery?: number; // 电量百分比 (0-100)
  sensors?: LaserSensor[]; // 多个激光传感器
  showInfoCard?: boolean; // 是否显示信息卡片
}

interface SceneManagerRef {
  loadedModels?: Record<string, any>;
  modelRefs?: Record<string, { getModel: () => any }>;
  getCamera?: () => THREE.Camera;
  getControls?: () => any;
  setFirstPersonMode?: (enabled: boolean) => void;
}

interface FirstPersonView {
  enabled: boolean;
  targetModelId: string | null;
  cameraOffset: { x: number; y: number; z: number };
  lookAtOffset: { x: number; y: number; z: number };
  followRotation: boolean;
  followMovement: boolean;
  smoothFollow: boolean;
  updateInterval: number | null;
  originalCameraPosition: THREE.Vector3;
  originalCameraRotation: THREE.Euler;
  originalControlsTarget: THREE.Vector3;
  presets: Record<string, { camera: { x: number; y: number; z: number }; lookAt: { x: number; y: number; z: number } }>;
  currentPreset: string;
}

const props = defineProps({
  loadingStatus: {
    type: Object as PropType<Record<string, 'loaded' | 'loading' | 'error'>>,
    default: () => ({}),
  },
  firstPersonView: {
    type: Object as PropType<FirstPersonView>,
    required: true,
  },
  models: {
    type: Array as PropType<Model[]>,
    default: () => [],
  },
  sceneManagerRef: {
    type: Object as PropType<SceneManagerRef>,
    required: true,
  },
  websocketSimulator: {
    type: Object,
    default: null,
  },
  pathExecutionSystem: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(['startFirstPersonUpdate', 'getSelectedModelByIdForFirstPerson', 'model-added', 'config-exported', 'config-imported', 'scene-settings-changed', 'info-cards-toggled', 'start-path-execution', 'stop-path-execution', 'update-speed-multiplier', 'map-loading', 'map-loaded', 'obstacle-added', 'obstacle-removed', 'set-camera-preset', 'set-custom-camera-position', 'smooth-animation-changed']);

const leftPanelCollapsed = ref(true);
const editingModelId = ref<string | null>(null);
const showSensorConfig = ref<string | null>(null);
const showAddDeviceModal = ref(false);
const showInfoCards = ref(false); // 信息卡片显示开关，默认关闭
const showMap = ref(true);
const showEnvironment = ref(false);

const handleTabClick = (tab: string) => {
  switch (tab) {
    case 'map':
      showMap.value = true;
      showEnvironment.value = false;
      break;
    case 'environment':
      showEnvironment.value = true;
      showMap.value = false;
      break;
  }
};

// 编辑表单数据
const editForm = ref({
  x: 0,
  z: 0,
  battery: 100,
  rotation: 0,
});

// 传感器配置表单数据
const sensorForm = ref<LaserSensor>({
  id: '',
  name: '',
  relativePosition: { x: 0, y: 0, z: 0 },
  pitchRange: { min: -45, max: 45 },
  yawRange: { min: -180, max: 180 },
  laserDensity: 10, // 每度10条激光线
  enabled: true,
});

const editingSensorIndex = ref<number>(-1); // -1表示新增，>=0表示编辑现有传感器

const toggleLeftPanel = () => {
  console.log('props', props, props.models, props.models.length);
  leftPanelCollapsed.value = !leftPanelCollapsed.value;
};

const totalModels = ref(props.models.length);

// 打开添加设备弹框
const openAddDeviceModal = () => {
  showAddDeviceModal.value = true;
};

// 关闭添加设备弹框
const closeAddDeviceModal = () => {
  showAddDeviceModal.value = false;
};

// 确认添加设备
const confirmAddDevice = (newModel: any) => {
  // 为新模型添加信息卡片显示状态（默认开启）
  newModel.showInfoCard = true;

  // 添加到models数组
  props.models.push(newModel);
  totalModels.value = props.models.length;

  // 发送事件给父组件
  emit('model-added', newModel);

  console.log('新设备已添加:', newModel);
};

// 导出场景配置
const exportSceneConfig = () => {
  const config = {
    timestamp: new Date().toISOString(),
    scene: {
      // 这里可以添加场景相关配置，如果需要的话
      background: 0x86c3f3,
      lights: [],
      camera: {}
    },
    models: props.models,
    totalModels: props.models.length
  };

  const jsonString = JSON.stringify(config, null, 2);
  const blob = new Blob([jsonString], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `scene-config-${Date.now()}.json`;
  a.click();
  URL.revokeObjectURL(url);

  emit('config-exported', config);
  console.log('场景配置已导出');
};

// 导入场景配置
const importSceneConfig = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];
  if (!file) return;

  const reader = new FileReader();
  reader.onload = (e) => {
    try {
      const config = JSON.parse(e.target?.result as string);
      if (config.models && Array.isArray(config.models)) {
        // 清空当前模型
        props.models.splice(0, props.models.length);

        // 添加导入的模型
        config.models.forEach((model: any) => {
          props.models.push(model);
        });

        totalModels.value = props.models.length;
        emit('config-imported', config);
        console.log('场景配置已导入:', config);
        alert(`成功导入 ${config.models.length} 个设备`);
      } else {
        alert('无效的配置文件格式');
      }
    } catch (error) {
      console.error('导入配置失败:', error);
      alert('导入配置失败，请检查文件格式');
    }
  };
  reader.readAsText(file);

  // 重置文件输入
  target.value = '';
};

// 清空所有设备
const clearAllDevices = () => {
  if (props.models.length === 0) {
    alert('当前没有设备需要清空');
    return;
  }

  if (confirm(`确定要清空所有 ${props.models.length} 个设备吗？`)) {
    props.models.splice(0, props.models.length);
    totalModels.value = 0;
    console.log('所有设备已清空');
  }
};

// 处理场景设置变化
const handleSceneSettingsChanged = (settings) => {
  emit('scene-settings-changed', settings);
  console.log('场景设置已更新:', settings);
};

// 处理流畅动画设置变化
const handleSmoothAnimationChanged = (animationSettings) => {
  emit('smooth-animation-changed', animationSettings);
  console.log('流畅动画设置已更新:', animationSettings);
};

// 切换信息卡片显示
const toggleInfoCards = () => {
  showInfoCards.value = !showInfoCards.value;
  console.log('左侧面板 - 信息卡片切换:', showInfoCards.value ? '开启' : '关闭');
  console.log('当前模型数量:', props.models.length);
  emit('info-cards-toggled', showInfoCards.value);
  console.log('事件已发送: info-cards-toggled', showInfoCards.value);
};

// 开始路径执行
const startPathExecution = () => {
  console.log('左侧面板 - 开始路径执行');
  emit('start-path-execution');
};

// 停止路径执行
const stopPathExecution = () => {
  console.log('左侧面板 - 停止路径执行');
  speedMultiplier.value = 1; // 重置速度倍数
  emit('stop-path-execution');
};

// 速度倍数控制
const speedMultiplier = ref(1);

// 更新速度倍数
const updateSpeedMultiplier = () => {
  console.log('左侧面板 - 更新执行速度:', speedMultiplier.value + 'x');
  emit('update-speed-multiplier', speedMultiplier.value);
};

// 设置指定速度
const setSpeed = (speed) => {
  speedMultiplier.value = speed;
  updateSpeedMultiplier();
};

// 地图相关数据
const availableMaps = ref([]);
const selectedMap = ref(null);
const currentMapName = ref('');
const isLoadingMap = ref(false);

// 加载地图配置
const loadMapConfig = async () => {
  try {
    const response = await fetch('/src/views/route_simulatton/left_part/components/map.json');
    const mapData = await response.json();
    availableMaps.value = mapData;
    console.log('地图配置加载成功:', mapData);
  } catch (error) {
    console.error('加载地图配置失败:', error);
    availableMaps.value = [];
  }
};

// 处理地图选择
const handleMapSelection = () => {
  console.log('🗺️ 选择地图:', selectedMap.value);
  if (selectedMap.value) {
    console.log('📋 选中地图详情:', {
      id: selectedMap.value.id,
      name: selectedMap.value.name,
      path: selectedMap.value.path,
      hasValidPath: !!selectedMap.value.path
    });
  }
};

// 获取地图状态
const getMapStatus = (map) => {
  if (!map.path) return '无效';
  if (!map.name) return '无名称';
  if (!map.id) return '无ID';
  return '可用';
};

// 加载选中的地图
const loadSelectedMap = async () => {
  if (!selectedMap.value || isLoadingMap.value) return;

  isLoadingMap.value = true;

  try {
    console.log('🗺️ 开始加载地图:', selectedMap.value.name);
    console.log('📋 地图配置:', selectedMap.value);

    // 发送地图开始加载事件到父组件
    emit('map-loading', selectedMap.value);

    // 发送地图配置到父组件进行实际加载
    emit('map-loaded', selectedMap.value);

    // 更新当前地图名称
    currentMapName.value = selectedMap.value.name;

    console.log('✅ 地图配置已传递到主页面');

  } catch (error) {
    console.error('地图加载失败:', error);
    alert('地图加载失败：' + error.message);
  } finally {
    isLoadingMap.value = false;
  }
};

const addNewModel = () => {
  const randomFiveDigits = Math.floor(Math.random() * 90000) + 10000; // 生成10000-99999的随机数
  const newModelId = `model_${randomFiveDigits}`;
  const newModel = {
    id: newModelId,
    name: `模型${randomFiveDigits}`,
    path: '/models/model3.glb',
    position: { x: 0, y: 0, z: -3 },
    rotation: { x: 0, y: 90, z: 0 },
    scale: 0.8,
    currentPosition: { x: 0, y: 0, z: 0 },
    currentRotation: { x: 0, y: 0, z: 0 },
    battery: 100, // 默认满电
    sensors: [], // 默认无传感器
    material: {
      color: 0x0080ff,
      roughness: 0.5, // 默认粗糙度
      metalness: 0.6, // 默认金属度
    },
  };
  props.models.push(newModel);
  totalModels.value++;
};

const removeModel = (modelId: string) => {
  const index = props.models.findIndex(model => model.id === modelId);
  if (index !== -1) {
    props.models.splice(index, 1);
  }
  nextTick(updateScrollIndicator);
};

const movementControls = ref({
  selectedModelId: 'car7',
  targetPosition: { x: 0, y: 0, z: 0 },
  movementSpeed: 2.0,
  isMoving: false,
  keyboardControlEnabled: true,
});

const enableFirstPersonView = (modelId: string) => {
  console.log('props.sceneManagerRef', props.sceneManagerRef);
  if (!props.sceneManagerRef) {
    console.warn('SceneManager not available');
    return;
  }

  if (props.firstPersonView.enabled) {
    disableFirstPersonView();
  }

  const getSelectedModelByIdForFirstPerson = (modelId: string) => {
    if (!props.sceneManagerRef) return null;
    try {
      const loadedModels = props.sceneManagerRef.loadedModels;
      if (loadedModels && loadedModels[modelId]) {
        return loadedModels[modelId];
      }
      const modelRef = props.sceneManagerRef.modelRefs?.[modelId];
      return modelRef?.getModel?.() || null;
    } catch (error) {
      console.error('Error getting model for first person view:', error);
      return null;
    }
  };

  const targetModel = getSelectedModelByIdForFirstPerson(modelId);
  const camera = props.sceneManagerRef?.getCamera?.();
  if (!targetModel || !camera) {
    console.warn(`Model ${modelId} or camera not found for first person view`);
    return;
  }

  props.firstPersonView.originalCameraPosition.copy(camera.position);
  props.firstPersonView.originalCameraRotation.copy(camera.rotation);

  const controls = props.sceneManagerRef.getControls?.();
  if (controls) {
    props.firstPersonView.originalControlsTarget.copy(controls.target);
  }

  if (props.sceneManagerRef.setFirstPersonMode) {
    props.sceneManagerRef.setFirstPersonMode(true);
  }

  movementControls.value.selectedModelId = modelId;
  props.firstPersonView.enabled = true;
  props.firstPersonView.targetModelId = modelId;

  emit('startFirstPersonUpdate');
  console.log(`First person view enabled for model: ${modelId}`);
  console.log(`Movement controls now target model: ${modelId}`);
};

const disableFirstPersonView = () => {
  if (!props.firstPersonView.enabled) return;

  const camera = props.sceneManagerRef?.getCamera?.();
  const controls = props.sceneManagerRef?.getControls?.();

  if (props.sceneManagerRef.setFirstPersonMode) {
    props.sceneManagerRef.setFirstPersonMode(false);
  }

  if (camera) {
    camera.position.copy(props.firstPersonView.originalCameraPosition);
    camera.rotation.copy(props.firstPersonView.originalCameraRotation);
  }

  if (controls) {
    controls.target.copy(props.firstPersonView.originalControlsTarget);
    controls.update();
  }

  props.firstPersonView.enabled = false;
  props.firstPersonView.targetModelId = null;

  if (props.firstPersonView.updateInterval) {
    cancelAnimationFrame(props.firstPersonView.updateInterval);
    props.firstPersonView.updateInterval = null;
  }

  console.log('First person view disabled and camera restored');
};

const toggleFirstPersonView = (id: string) => {
  if (props.firstPersonView.enabled && props.firstPersonView.targetModelId === id) {
    disableFirstPersonView();
  } else {
    enableFirstPersonView(id);
  }
};

// 相机设置相关方法
const setCameraPreset = (presetName: string) => {
  console.log('📷 设置相机预设:', presetName);
  emit('set-camera-preset', presetName);
};

const updateCustomCameraPosition = () => {
  const cameraPos = {
    x: props.firstPersonView.cameraOffset?.x || 0,
    y: props.firstPersonView.cameraOffset?.y || 5,
    z: props.firstPersonView.cameraOffset?.z || 0
  };
  const lookAtPos = {
    x: props.firstPersonView.lookAtOffset?.x || 0,
    y: props.firstPersonView.lookAtOffset?.y || 5,
    z: props.firstPersonView.lookAtOffset?.z || -10
  };

  console.log('🎯 更新自定义相机位置:', { camera: cameraPos, lookAt: lookAtPos });
  emit('set-custom-camera-position', cameraPos, lookAtPos);
};

// 切换单个模型的信息卡片显示状态
const toggleModelInfoCard = (model: Model) => {
  const oldState = model.showInfoCard;
  model.showInfoCard = !model.showInfoCard;

  // 如果用户开启了单个设备的信息卡片，但全局开关是关闭的，自动开启全局开关
  if (model.showInfoCard && !showInfoCards.value) {
    console.log('📋 自动开启全局信息卡片开关');
    showInfoCards.value = true;
    emit('info-cards-toggled', showInfoCards.value);
  }

  console.log(`📋 模型 ${model.name} 信息卡片状态变化:`, {
    modelId: model.id,
    oldState: oldState,
    newState: model.showInfoCard,
    globalSwitch: showInfoCards.value,
    action: model.showInfoCard ? '显示' : '隐藏'
  });

  // 触发响应式更新
  nextTick(() => {
    console.log(`📋 nextTick 后模型 ${model.name} 的 showInfoCard:`, model.showInfoCard);
    console.log(`📋 nextTick 后全局开关状态:`, showInfoCards.value);
  });
};

// 编辑功能
const startEdit = (model: any) => {
  editingModelId.value = model.id;
  editForm.value = {
    x: model.currentPosition.x,
    z: model.currentPosition.z,
    battery: model.battery || 100,
    rotation: model.currentRotation.y,
  };
};

const cancelEdit = () => {
  editingModelId.value = null;
};

const saveEdit = (model: any) => {
  // 更新模型数据
  model.currentPosition.x = editForm.value.x;
  model.currentPosition.z = editForm.value.z;
  model.battery = editForm.value.battery;
  model.currentRotation.y = editForm.value.rotation;

  // 退出编辑模式
  editingModelId.value = null;

  // 这里可以添加保存到后端的逻辑
  console.log('Model updated:', model);
};

// 传感器配置功能
const openSensorConfig = (model: any) => {
  showSensorConfig.value = model.id;
  // 确保模型有sensors数组
  if (!model.sensors) {
    model.sensors = [];
  }
};

const closeSensorConfig = () => {
  showSensorConfig.value = null;
  editingSensorIndex.value = -1;
};

const addNewSensor = (model: any) => {
  const newSensorId = `sensor_${Date.now()}`;
  sensorForm.value = {
    id: newSensorId,
    name: `激光传感器${(model.sensors?.length || 0) + 1}`,
    relativePosition: { x: 0, y: 0, z: 0 },
    pitchRange: { min: -45, max: 45 },
    yawRange: { min: -180, max: 180 },
    laserDensity: 10,
    enabled: true,
  };
  editingSensorIndex.value = -1; // 新增模式
};

const editSensor = (model: any, index: number) => {
  const sensor = model.sensors[index];
  sensorForm.value = { ...sensor };
  editingSensorIndex.value = index;
};

const saveSensor = (model: any) => {
  if (!model.sensors) {
    model.sensors = [];
  }

  if (editingSensorIndex.value === -1) {
    // 新增传感器
    model.sensors.push({ ...sensorForm.value });
  } else {
    // 编辑现有传感器
    model.sensors[editingSensorIndex.value] = { ...sensorForm.value };
  }

  editingSensorIndex.value = -1;
  console.log('Sensor saved:', sensorForm.value);
};

const deleteSensor = (model: any, index: number) => {
  if (model.sensors && index >= 0 && index < model.sensors.length) {
    model.sensors.splice(index, 1);
    console.log('Sensor deleted at index:', index);
  }
};

const toggleSensorEnabled = (sensor: LaserSensor) => {
  sensor.enabled = !sensor.enabled;
  console.log('Sensor toggled:', sensor.name, sensor.enabled);
};

// 获取模型状态
const getModelStatus = (model: any) => {
  const loadingStatus = props.loadingStatus[model.id];
  const battery = model.battery || 100;

  if (loadingStatus === 'error') return { status: '错误', color: 'red' };
  if (loadingStatus === 'loading') return { status: '加载中', color: 'yellow' };
  if (battery < 30) return { status: '低电量', color: 'orange' };
  return { status: '在线', color: 'green' };
};

const scrollContentRef = ref<HTMLElement | null>(null);
const scrollIndicatorRef = ref<HTMLElement | null>(null);
const indicatorHeight = ref(0);
const indicatorTop = ref(0);

const updateScrollIndicator = () => {
  const contentEl = scrollContentRef.value;
  const indicatorEl = scrollIndicatorRef.value;
  if (contentEl && indicatorEl) {
    const { scrollTop, scrollHeight, clientHeight } = contentEl;
    if (scrollHeight <= clientHeight) {
      indicatorHeight.value = 0;
      indicatorTop.value = 0;
      indicatorEl.style.height = `0px`;
      indicatorEl.style.transform = `translateY(0px)`;
      return;
    }
    const scrollRatio = clientHeight / scrollHeight;
    indicatorHeight.value = clientHeight * scrollRatio;
    const scrollableHeight = scrollHeight - clientHeight;
    const indicatorTravelHeight = clientHeight - indicatorHeight.value;
    indicatorTop.value = (scrollTop / scrollableHeight) * indicatorTravelHeight;
    indicatorEl.style.height = `${indicatorHeight.value}px`;
    indicatorEl.style.transform = `translateY(${indicatorTop.value}px)`;
  }
};

const handleScroll = () => {
  updateScrollIndicator();
};

onMounted(async () => {
  nextTick(() => {
    updateScrollIndicator();
  });
  window.addEventListener('resize', updateScrollIndicator);

  // 加载地图配置
  await loadMapConfig();
});

onUnmounted(() => {
  window.removeEventListener('resize', updateScrollIndicator);
});

// 障碍物相关数据
const obstacles = ref([]);
const isAddingObstacle = ref(false);
const showAddObstacleModalVisible = ref(false);

// 显示添加障碍物模态框
const showAddObstacleModal = () => {
  showAddObstacleModalVisible.value = true;
};

// 处理添加障碍物（从模态框确认）
const handleAddObstacle = async (obstacleFormData) => {
  if (isAddingObstacle.value) return;

  isAddingObstacle.value = true;

  try {
    console.log('🚶 开始添加障碍物...', obstacleFormData);

    // 生成障碍物数据
    const obstacleData = {
      id: `obstacle_${Date.now()}`,
      name: obstacleFormData.name,
      type: 'obstacle',
      path: '/models/person2.glb',
      position: { ...obstacleFormData.position },
      rotation: { ...obstacleFormData.rotation },
      scale: { ...obstacleFormData.scale },
      color: obstacleFormData.color,
      battery: obstacleFormData.battery,
      consume: 0,
      // 默认移动路径（隐式加载）
      defaultPath: [
        {
          position: { x: obstacleFormData.position.x, y: obstacleFormData.position.y, z: obstacleFormData.position.z },
          rotation: { x: obstacleFormData.rotation.x, y: obstacleFormData.rotation.y, z: obstacleFormData.rotation.z },
          duration: 2000,
          power: obstacleFormData.battery - 10
        },
        {
          position: { x: obstacleFormData.position.x + 20, y: obstacleFormData.position.y, z: obstacleFormData.position.z + 20 },
          rotation: { x: obstacleFormData.rotation.x, y: obstacleFormData.rotation.y + Math.PI / 2, z: obstacleFormData.rotation.z },
          duration: 3000,
          power: obstacleFormData.battery - 30
        },
        {
          position: { x: obstacleFormData.position.x - 20, y: obstacleFormData.position.y, z: obstacleFormData.position.z + 20 },
          rotation: { x: obstacleFormData.rotation.x, y: obstacleFormData.rotation.y + Math.PI, z: obstacleFormData.rotation.z },
          duration: 3000,
          power: obstacleFormData.battery - 50
        },
        {
          position: { x: obstacleFormData.position.x, y: obstacleFormData.position.y, z: obstacleFormData.position.z },
          rotation: { x: obstacleFormData.rotation.x, y: obstacleFormData.rotation.y + Math.PI * 1.5, z: obstacleFormData.rotation.z },
          duration: 2000,
          power: obstacleFormData.battery - 70
        }
      ]
    };

    // 添加到障碍物列表
    obstacles.value.push(obstacleData);

    // 发送事件到父组件
    emit('obstacle-added', obstacleData);

    console.log('✅ 障碍物添加成功:', obstacleData.name);

  } catch (error) {
    console.error('添加障碍物失败:', error);
    alert('添加障碍物失败：' + error.message);
  } finally {
    isAddingObstacle.value = false;
  }
};

// 删除障碍物
const removeObstacle = (obstacleId) => {
  const index = obstacles.value.findIndex(obs => obs.id === obstacleId);
  if (index !== -1) {
    const obstacle = obstacles.value[index];
    obstacles.value.splice(index, 1);

    // 发送事件到父组件
    emit('obstacle-removed', obstacleId);

    console.log('障碍物已删除:', obstacle.name);
  }
};

watch(
  () => props.models.length,
  () => {
    nextTick(updateScrollIndicator);
  },
);
</script>

<template>
  <div
    :class="[
      'left_part absolute top-0 left-0 h-[calc(45vh-4.5rem)] transition-all duration-300 ease-in-out z-10',
      leftPanelCollapsed ? 'w-12' : 'w-90',
    ]"
  >
    <!-- Collapse Button -->
    <button
      @click="toggleLeftPanel"
      :class="[
        'absolute top-4 z-20 w-8 h-8 bg-slate-800/80 hover:bg-slate-700/90 backdrop-blur-sm rounded flex items-center justify-center text-white shadow-lg border border-cyan-400/30 transition-all duration-200',
        leftPanelCollapsed ? 'left-2' : 'right-2',
      ]"
    >
      <svg
        class="w-4 h-4 transition-transform duration-200"
        :class="{ 'rotate-180': leftPanelCollapsed }"
        fill="currentColor"
        viewBox="0 0 24 24"
      >
        <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z" />
      </svg>
    </button>

    <!-- Panel Content -->
    <div v-if="!leftPanelCollapsed" class="h-full bg-slate-900/15 border-r border-cyan-500/20 shadow-xl relative">
      <!-- Custom Scroll Indicator Line -->
      <div
        ref="scrollIndicatorRef"
        class="absolute left-0 top-0 w-1 bg-cyan-400 rounded-full transition-all duration-100 ease-out"
        style="height: 0px; transform: translateY(0px)"
      ></div>

      <div
        ref="scrollContentRef"
        class="h-full overflow-y-auto p-2 relative z-10 scrollbar-hide"
        @scroll="handleScroll"
      >
        <div class="space-y-4">
          <!-- Panel Title -->
          <div class="text-center mb-2">
            <div class="relative py-1">
              <h2 class="text-lg font-bold text-white mb-1 relative z-10">场景配置</h2>
              <!-- Header Line -->
              <div
                class="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-2/5 h-px bg-gradient-to-r from-transparent via-cyan-400 to-transparent"
              ></div>
            </div>
          </div>

          <!-- 地图加载 -->
          <div
            class="futuristic-card relative overflow-hidden group bg-gradient-to-br from-slate-800/80 to-slate-700/60 border border-cyan-500/30 shadow-lg shadow-cyan-500/10 hover:shadow-cyan-500/20 transition-all duration-300"
          >
            <!-- Card Glow Effect -->
            <div
              class="absolute inset-0 bg-gradient-to-r from-cyan-500/5 via-transparent to-cyan-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
            ></div>
            <div
              class="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-cyan-400/50 to-transparent"
            ></div>

            <div class="relative z-10">
              <div class="p-4">
                <div class="flex items-center gap-3 mb-4">
                  <div class="tab_btn flex items-center gap-2 cursor-pointer p-2 rounded-md" :class="{ 'bg-blue-500/50': showMap }" @click="handleTabClick('map')">
                    <div class="w-2 h-2 rounded-full shrink-0 bg-blue-500 shadow-lg shadow-blue-500/50"></div>
                    <h3 class="text-sm font-semibold text-white m-0">地图加载</h3>
                  </div>
                  <div class="tab_btn flex items-center gap-2 cursor-pointer p-2 rounded-md" :class="{ 'bg-purple-500/50': showEnvironment }" @click="handleTabClick('environment')">
                    <div class="w-2 h-2 rounded-full shrink-0 bg-purple-500 shadow-lg shadow-purple-500/50"></div>
                    <h3 class="text-sm font-semibold text-white m-0">环境控制</h3>
                  </div>
                </div>

                <div class="space-y-3" v-if="showMap">
                  <!-- 当前地图显示 -->
                  <div class="text-xs text-slate-400">
                    当前地图:
                    <span class="text-blue-400">{{ currentMapName || '未加载' }}</span>
                  </div>

                  <!-- 地图列表 -->
                  <div class="space-y-2 max-h-40 overflow-y-auto">
                    <div
                      v-for="map in availableMaps"
                      :key="map.id + '_' + map.name"
                      class="flex items-center gap-2 p-2 rounded border border-slate-600/50 hover:border-slate-500 transition-colors duration-200"
                    >
                      <input
                        type="radio"
                        :id="`map_${map.id}_${map.name}`"
                        :value="map"
                        v-model="selectedMap"
                        @change="handleMapSelection"
                        class="w-3 h-3 text-blue-500 bg-slate-700 border-slate-600 focus:ring-blue-500 focus:ring-2"
                      >
                      <label
                        :for="`map_${map.id}_${map.name}`"
                        class="text-xs text-slate-300 cursor-pointer flex-1"
                      >
                        {{ map.name }}
                      </label>
                      <span class="text-xs text-slate-500">
                        {{ getMapStatus(map) }}
                      </span>
                    </div>
                  </div>

                  <!-- 加载按钮 -->
                  <button
                    @click="loadSelectedMap"
                    :disabled="!selectedMap || isLoadingMap"
                    :class="[
                      'w-full px-3 py-2 text-xs rounded border transition-all duration-200',
                      selectedMap && !isLoadingMap
                        ? 'bg-blue-500/20 border-blue-400 text-blue-300 hover:bg-blue-500/30'
                        : 'bg-slate-600/60 border-slate-500 text-slate-400 cursor-not-allowed'
                    ]"
                  >
                    <span v-if="isLoadingMap">🔄 加载中...</span>
                    <span v-else>🗺️ 加载地图</span>
                  </button>
                </div>
                <SceneManager
                  v-if="showEnvironment"
                  :sceneManagerRef="props.sceneManagerRef"
                  :websocketSimulator="props.websocketSimulator"
                  @scene-settings-changed="handleSceneSettingsChanged"
                  @smooth-animation-changed="handleSmoothAnimationChanged"
                  class=""
                />
              </div>
            </div>
          </div>

          <!-- 场景管理 -->
          <!-- <SceneManager
            :sceneManagerRef="props.sceneManagerRef"
            :websocketSimulator="props.websocketSimulator"
            @scene-settings-changed="handleSceneSettingsChanged"
            @smooth-animation-changed="handleSmoothAnimationChanged"
          /> -->

          <!-- Model Cards -->
          <div
            v-for="model in props.models"
            :key="model.id"
            class="futuristic-card relative overflow-hidden group bg-gradient-to-br from-slate-800/80 to-slate-700/60 border border-cyan-500/30 shadow-lg shadow-cyan-500/10 hover:shadow-cyan-500/20 transition-all duration-300"
          >
            <!-- Card Glow Effect -->
            <div
              class="absolute inset-0 bg-gradient-to-br from-cyan-400/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-md"
            ></div>

            <!-- Corner Decorations -->
            <div class="absolute top-2 left-2 w-4 h-4 border-l-2 border-t-2 border-cyan-400/60"></div>
            <div class="absolute top-2 right-2 w-4 h-4 border-r-2 border-t-2 border-cyan-400/60"></div>
            <div class="absolute bottom-2 left-2 w-4 h-4 border-l-2 border-b-2 border-cyan-400/60"></div>
            <div class="absolute bottom-2 right-2 w-4 h-4 border-r-2 border-b-2 border-cyan-400/60"></div>

            <!-- Card Content -->
            <div class="relative z-10 p-6">
              <!-- Header Section -->
              <div class="flex justify-between items-center mb-4">
                <!-- Model ID Display -->
                <div
                  class="model-id-display relative px-4 py-2 bg-gradient-to-r from-cyan-500/20 to-cyan-500/10 border border-cyan-500/50"
                >
                  <span class="text-cyan-400 text-lg font-bold">{{ model.id.toUpperCase() }}</span>
                </div>

                <!-- Status Indicator -->
                <div class="flex items-center space-x-3">
                  <div class="relative">
                    <!-- Status Circle -->
                    <div
                      class="w-6 h-6 rounded-full border-2 flex items-center justify-center relative"
                      :class="{
                        'border-green-500 bg-green-500/10': getModelStatus(model).color === 'green',
                        'border-red-500 bg-red-500/10': getModelStatus(model).color === 'red',
                        'border-yellow-500 bg-yellow-500/10 animate-pulse': getModelStatus(model).color === 'yellow',
                        'border-orange-500 bg-orange-500/10': getModelStatus(model).color === 'orange',
                      }"
                    >
                      <!-- Inner Dot -->
                      <div
                        class="w-2 h-2 rounded-full"
                        :class="{
                          'bg-green-500': getModelStatus(model).color === 'green',
                          'bg-red-500': getModelStatus(model).color === 'red',
                          'bg-yellow-500': getModelStatus(model).color === 'yellow',
                          'bg-orange-500': getModelStatus(model).color === 'orange',
                        }"
                      ></div>
                      <!-- Pulse Ring -->
                      <div
                        v-if="getModelStatus(model).color === 'green'"
                        class="absolute inset-0 rounded-full border-2 border-green-500 animate-ping opacity-75"
                      ></div>
                    </div>
                  </div>
                  <span
                    class="text-sm font-medium"
                    :class="{
                      'text-green-400': getModelStatus(model).color === 'green',
                      'text-red-400': getModelStatus(model).color === 'red',
                      'text-yellow-400': getModelStatus(model).color === 'yellow',
                      'text-orange-400': getModelStatus(model).color === 'orange',
                    }"
                  >
                    {{ getModelStatus(model).status }}
                  </span>
                </div>
              </div>

              <!-- Model Name with Battery -->
              <div class="mb-4">
                <div class="flex items-center justify-between mb-2">
                  <h3 class="text-white text-xl font-bold">{{ model.name }}</h3>

                  <!-- Battery Display -->
                  <div class="flex items-center space-x-2">
                    <!-- Battery Icon -->
                    <div class="relative">
                      <!-- Battery Body -->
                      <div class="w-8 h-4 border border-slate-400 rounded-sm bg-slate-800 relative overflow-hidden">
                        <!-- Battery Fill -->
                        <div
                          class="h-full transition-all duration-300 rounded-sm"
                          :style="{ width: `${model.battery || 100}%` }"
                          :class="{
                            'bg-green-500': (model.battery || 100) >= 50,
                            'bg-yellow-500': (model.battery || 100) >= 30 && (model.battery || 100) < 50,
                            'bg-red-500': (model.battery || 100) < 30
                          }"
                        ></div>
                        <!-- Low Battery Warning -->
                        <div
                          v-if="(model.battery || 100) < 30"
                          class="absolute inset-0 bg-red-500/20 animate-pulse"
                        ></div>
                      </div>
                      <!-- Battery Tip -->
                      <div class="absolute -right-0.5 top-1 w-1 h-2 bg-slate-400 rounded-r-sm"></div>
                    </div>
                    <!-- Battery Percentage -->
                    <span
                      class="text-xs font-mono font-semibold"
                      :class="{
                        'text-green-400': (model.battery || 100) >= 50,
                        'text-yellow-400': (model.battery || 100) >= 30 && (model.battery || 100) < 50,
                        'text-red-400': (model.battery || 100) < 30
                      }"
                    >
                      {{ (model.battery || 100) }}%
                    </span>
                  </div>
                </div>
                <div class="w-12 h-0.5 bg-gradient-to-r from-cyan-400 to-transparent"></div>
              </div>

              <!-- Stats Section / Edit Form -->
              <div v-if="editingModelId === model.id" class="mb-6 space-y-4">
                <!-- Edit Form -->
                <div class="bg-slate-800/60 border border-cyan-500/30 p-4 rounded">
                  <h4 class="text-cyan-300 text-sm font-medium mb-3">编辑模型参数</h4>

                  <!-- Position Inputs -->
                  <div class="grid grid-cols-2 gap-3 mb-3">
                    <div>
                      <label class="text-xs text-slate-400 block mb-1">X坐标</label>
                      <input
                        v-model.number="editForm.x"
                        type="number"
                        step="0.1"
                        class="w-full px-2 py-1 text-sm bg-slate-700 border border-slate-600 rounded text-white focus:border-cyan-400 focus:outline-none"
                      />
                    </div>
                    <div>
                      <label class="text-xs text-slate-400 block mb-1">Z坐标</label>
                      <input
                        v-model.number="editForm.z"
                        type="number"
                        step="0.1"
                        class="w-full px-2 py-1 text-sm bg-slate-700 border border-slate-600 rounded text-white focus:border-cyan-400 focus:outline-none"
                      />
                    </div>
                  </div>

                  <!-- Battery and Rotation -->
                  <div class="grid grid-cols-2 gap-3 mb-4">
                    <div>
                      <label class="text-xs text-slate-400 block mb-1 flex items-center gap-1">
                        <!-- Mini Battery Icon -->
                        <div class="relative">
                          <div class="w-4 h-2 border border-slate-400 rounded-sm bg-slate-800 relative overflow-hidden">
                            <div
                              class="h-full transition-all duration-300 rounded-sm"
                              :style="{ width: `${editForm.battery}%` }"
                              :class="{
                                'bg-green-500': editForm.battery >= 50,
                                'bg-yellow-500': editForm.battery >= 30 && editForm.battery < 50,
                                'bg-red-500': editForm.battery < 30
                              }"
                            ></div>
                          </div>
                          <div class="absolute -right-0.5 top-0.5 w-0.5 h-1 bg-slate-400 rounded-r-sm"></div>
                        </div>
                        电量 (%)
                      </label>
                      <input
                        v-model.number="editForm.battery"
                        type="number"
                        min="0"
                        max="100"
                        class="w-full px-2 py-1 text-sm bg-slate-700 border border-slate-600 rounded text-white focus:border-cyan-400 focus:outline-none"
                      />
                    </div>
                    <div>
                      <label class="text-xs text-slate-400 block mb-1">角度 (°)</label>
                      <input
                        v-model.number="editForm.rotation"
                        type="number"
                        step="1"
                        class="w-full px-2 py-1 text-sm bg-slate-700 border border-slate-600 rounded text-white focus:border-cyan-400 focus:outline-none"
                      />
                    </div>
                  </div>

                  <!-- Edit Buttons -->
                  <div class="flex gap-2">
                    <button
                      @click="saveEdit(model)"
                      class="btn-3d flex-1 px-3 py-2 text-xs bg-gradient-to-br from-green-600 to-green-700 hover:from-green-500 hover:to-green-600 active:from-green-700 active:to-green-800 text-white rounded transition-all duration-150 font-medium shadow-lg shadow-green-500/20 hover:shadow-xl hover:shadow-green-500/30 active:shadow-md active:shadow-green-500/40 relative overflow-hidden group"
                    >
                      <!-- 3D Highlights and Shadows -->
                      <div class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-green-300/40 to-transparent"></div>
                      <div class="absolute top-0 left-0 bottom-0 w-px bg-gradient-to-b from-green-300/40 via-green-300/20 to-transparent"></div>
                      <div class="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-green-900/60 to-transparent"></div>
                      <div class="absolute top-0 right-0 bottom-0 w-px bg-gradient-to-b from-transparent via-green-900/40 to-green-900/60"></div>

                      <span class="relative z-10 transition-transform duration-150 group-active:translate-y-0.5">保存</span>
                    </button>
                    <button
                      @click="cancelEdit"
                      class="btn-3d flex-1 px-3 py-2 text-xs bg-gradient-to-br from-gray-600 to-gray-700 hover:from-gray-500 hover:to-gray-600 active:from-gray-700 active:to-gray-800 text-white rounded transition-all duration-150 font-medium shadow-lg shadow-gray-500/20 hover:shadow-xl hover:shadow-gray-500/30 active:shadow-md active:shadow-gray-500/40 relative overflow-hidden group"
                    >
                      <!-- 3D Highlights and Shadows -->
                      <div class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-300/40 to-transparent"></div>
                      <div class="absolute top-0 left-0 bottom-0 w-px bg-gradient-to-b from-gray-300/40 via-gray-300/20 to-transparent"></div>
                      <div class="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-900/60 to-transparent"></div>
                      <div class="absolute top-0 right-0 bottom-0 w-px bg-gradient-to-b from-transparent via-gray-900/40 to-gray-900/60"></div>

                      <span class="relative z-10 transition-transform duration-150 group-active:translate-y-0.5">取消</span>
                    </button>
                  </div>
                </div>
              </div>

              <div v-else class="flex gap-3 mb-6 flex-wrap">
                <!-- Position Stat -->
                <div class="stat-item flex-1 min-w-0 relative bg-slate-700/60 border border-cyan-500/30 p-2">
                  <div class="text-center">
                    <div class="text-xs text-slate-400 font-medium mb-1">POS</div>
                    <div class="text-sm text-slate-200 font-mono font-semibold">
                      {{ model.currentPosition.x.toFixed(1) }}, {{ model.currentPosition.z.toFixed(1) }}
                    </div>
                  </div>
                </div>

                <!-- Rotation Stat -->
                <div class="stat-item flex-1 min-w-0 relative bg-slate-700/60 border border-cyan-500/30 p-2">
                  <div class="text-center">
                    <div class="text-xs text-slate-400 font-medium mb-1">ROT</div>
                    <div class="text-sm text-slate-200 font-mono font-semibold">
                      {{ model.currentRotation.y.toFixed(0) }}°
                    </div>
                  </div>
                </div>

                <!-- Sensors Stat -->
                <div class="stat-item flex-1 min-w-0 relative bg-slate-700/60 border border-cyan-500/30 p-2">
                  <div class="text-center">
                    <div class="text-xs text-slate-400 font-medium mb-1">传感器</div>
                    <div class="text-sm text-slate-200 font-mono font-semibold">
                      {{ (model.sensors?.length || 0) }}个
                      <span v-if="model.sensors?.some(s => s.enabled)" class="text-green-400">●</span>
                      <span v-else class="text-gray-400">○</span>
                    </div>
                  </div>
                </div>

                <!-- FPV Status -->
                <div
                  v-if="props.firstPersonView.targetModelId === model.id"
                  class="stat-item flex-1 min-w-0 relative bg-cyan-500/20 border border-cyan-400 p-2"
                >
                  <div class="text-center">
                    <div class="text-xs text-cyan-300 font-medium mb-1">👁️</div>
                    <div class="text-sm text-cyan-200 font-mono font-semibold">FPV</div>
                  </div>
                </div>
              </div>

              <!-- Control Buttons -->
              <div class="space-y-3">
                <!-- First Row: FPV and Edit -->
                <div class="flex gap-3">
                  <!-- First Person View Button -->
                  <button
                    @click="toggleFirstPersonView(model.id)"
                    :class="[
                      'control-btn btn-3d flex-1 h-10 relative overflow-hidden group transition-all duration-150 font-medium text-sm shadow-lg hover:shadow-xl active:shadow-md',
                      props.firstPersonView.targetModelId === model.id
                        ? 'bg-gradient-to-r from-green-600/50 to-green-500/30 border border-green-500 text-green-300 hover:from-green-600/70 hover:to-green-500/50 active:from-green-700/80 active:to-green-600/60 shadow-green-500/20 hover:shadow-green-500/30 active:shadow-green-500/40'
                        : 'bg-gradient-to-r from-cyan-600/50 to-cyan-500/30 border border-cyan-500 text-cyan-300 hover:from-cyan-600/70 hover:to-cyan-500/50 active:from-cyan-700/80 active:to-cyan-600/60 shadow-cyan-500/20 hover:shadow-cyan-500/30 active:shadow-cyan-500/40',
                      { 'opacity-50 cursor-not-allowed': props.loadingStatus[model.id] !== 'loaded' },
                    ]"
                    :disabled="props.loadingStatus[model.id] !== 'loaded'"
                  >
                    <!-- 3D Highlights and Shadows -->
                    <div class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
                    <div class="absolute top-0 left-0 bottom-0 w-px bg-gradient-to-b from-white/20 via-white/10 to-transparent"></div>
                    <div class="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-black/30 to-transparent"></div>
                    <div class="absolute top-0 right-0 bottom-0 w-px bg-gradient-to-b from-transparent via-black/20 to-black/30"></div>

                    <span class="relative z-10 transition-transform duration-150 group-active:translate-y-0.5">
                      {{ props.firstPersonView.targetModelId === model.id ? '退出视角' : '进入视角' }}
                    </span>
                    <!-- Button Glow -->
                    <div
                      class="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"
                      :class="
                        props.firstPersonView.targetModelId === model.id
                          ? 'bg-gradient-to-r from-green-400/30 to-transparent'
                          : 'bg-gradient-to-r from-cyan-400/30 to-transparent'
                      "
                    ></div>
                  </button>

                  <!-- Edit Button -->
                  <button
                    @click="startEdit(model)"
                    v-if="editingModelId !== model.id"
                    class="control-btn btn-3d px-4 h-10 relative overflow-hidden group bg-gradient-to-r from-blue-600/50 to-blue-500/30 border border-blue-500 text-blue-400 hover:from-blue-600/70 hover:to-blue-500/50 active:from-blue-700/80 active:to-blue-600/60 transition-all duration-150 font-medium text-sm shadow-lg shadow-blue-500/20 hover:shadow-xl hover:shadow-blue-500/30 active:shadow-md active:shadow-blue-500/40"
                  >
                    <!-- 3D Highlights and Shadows -->
                    <div class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-blue-300/30 to-transparent"></div>
                    <div class="absolute top-0 left-0 bottom-0 w-px bg-gradient-to-b from-blue-300/30 via-blue-300/15 to-transparent"></div>
                    <div class="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-blue-900/50 to-transparent"></div>
                    <div class="absolute top-0 right-0 bottom-0 w-px bg-gradient-to-b from-transparent via-blue-900/30 to-blue-900/50"></div>

                    <span class="relative z-10 transition-transform duration-150 group-active:translate-y-0.5">编辑</span>
                    <!-- Button Glow -->
                    <div
                      class="absolute inset-0 bg-gradient-to-r from-blue-400/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"
                    ></div>
                  </button>
                </div>

                <!-- Camera Position Settings (only show when FPV is active) -->
                <div v-if="props.firstPersonView.targetModelId === model.id" class="mt-3 p-3 bg-slate-800/60 border border-cyan-500/30 rounded">
                  <div class="text-xs text-cyan-400 font-medium mb-3">📷 相机位置设置</div>

                  <!-- Camera Presets -->
                  <div class="grid grid-cols-2 gap-2 mb-3">
                    <button
                      v-for="(preset, key) in firstPersonView.presets"
                      :key="key"
                      @click="setCameraPreset(key)"
                      :class="[
                        'px-2 py-1 text-xs rounded border transition-all duration-200',
                        firstPersonView.currentPreset === key
                          ? 'bg-cyan-500/30 border-cyan-400 text-cyan-300'
                          : 'bg-slate-700/60 border-slate-600 text-slate-300 hover:bg-slate-600/60'
                      ]"
                    >
                      {{ key === 'driver' ? '驾驶员' : key === 'overhead' ? '俯视' : key === 'follow' ? '跟随' : key === 'side' ? '侧视' : '自定义' }}
                    </button>
                  </div>

                  <!-- Custom Position Controls -->
                  <div v-if="firstPersonView.currentPreset === 'custom'" class="space-y-2">
                    <div class="text-xs text-slate-400 mb-2">相机位置 (模型坐标系)</div>
                    <div class="grid grid-cols-3 gap-1">
                      <input
                        v-model.number="firstPersonView.cameraOffset.x"
                        @input="updateCustomCameraPosition"
                        type="number"
                        step="0.5"
                        placeholder="X"
                        class="px-2 py-1 text-xs bg-slate-700/60 border border-slate-600 text-white rounded focus:border-cyan-400 focus:outline-none"
                      />
                      <input
                        v-model.number="firstPersonView.cameraOffset.y"
                        @input="updateCustomCameraPosition"
                        type="number"
                        step="0.5"
                        placeholder="Y"
                        class="px-2 py-1 text-xs bg-slate-700/60 border border-slate-600 text-white rounded focus:border-cyan-400 focus:outline-none"
                      />
                      <input
                        v-model.number="firstPersonView.cameraOffset.z"
                        @input="updateCustomCameraPosition"
                        type="number"
                        step="0.5"
                        placeholder="Z"
                        class="px-2 py-1 text-xs bg-slate-700/60 border border-slate-600 text-white rounded focus:border-cyan-400 focus:outline-none"
                      />
                    </div>

                    <div class="text-xs text-slate-400 mb-2">朝向目标 (模型坐标系)</div>
                    <div class="grid grid-cols-3 gap-1">
                      <input
                        v-model.number="firstPersonView.lookAtOffset.x"
                        @input="updateCustomCameraPosition"
                        type="number"
                        step="0.5"
                        placeholder="X"
                        class="px-2 py-1 text-xs bg-slate-700/60 border border-slate-600 text-white rounded focus:border-cyan-400 focus:outline-none"
                      />
                      <input
                        v-model.number="firstPersonView.lookAtOffset.y"
                        @input="updateCustomCameraPosition"
                        type="number"
                        step="0.5"
                        placeholder="Y"
                        class="px-2 py-1 text-xs bg-slate-700/60 border border-slate-600 text-white rounded focus:border-cyan-400 focus:outline-none"
                      />
                      <input
                        v-model.number="firstPersonView.lookAtOffset.z"
                        @input="updateCustomCameraPosition"
                        type="number"
                        step="0.5"
                        placeholder="Z"
                        class="px-2 py-1 text-xs bg-slate-700/60 border border-slate-600 text-white rounded focus:border-cyan-400 focus:outline-none"
                      />
                    </div>
                  </div>
                </div>

                <!-- Second Row: Sensor Config and Remove -->
                <div class="flex gap-3">
                  <!-- Sensor Config Button -->
                  <button
                    @click="openSensorConfig(model)"
                    class="control-btn btn-3d flex-1 h-10 relative overflow-hidden group bg-gradient-to-r from-purple-600/50 to-purple-500/30 border border-purple-500 text-purple-400 hover:from-purple-600/70 hover:to-purple-500/50 active:from-purple-700/80 active:to-purple-600/60 transition-all duration-150 font-medium text-sm shadow-lg shadow-purple-500/20 hover:shadow-xl hover:shadow-purple-500/30 active:shadow-md active:shadow-purple-500/40"
                  >
                    <!-- 3D Highlights and Shadows -->
                    <div class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-purple-300/30 to-transparent"></div>
                    <div class="absolute top-0 left-0 bottom-0 w-px bg-gradient-to-b from-purple-300/30 via-purple-300/15 to-transparent"></div>
                    <div class="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-purple-900/50 to-transparent"></div>
                    <div class="absolute top-0 right-0 bottom-0 w-px bg-gradient-to-b from-transparent via-purple-900/30 to-purple-900/50"></div>

                    <span class="relative z-10 transition-transform duration-150 group-active:translate-y-0.5">传感器配置</span>
                    <!-- Button Glow -->
                    <div
                      class="absolute inset-0 bg-gradient-to-r from-purple-400/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"
                    ></div>
                  </button>

                  <!-- Remove Button -->
                  <button
                    @click="removeModel(model.id)"
                    class="control-btn btn-3d px-4 h-10 relative overflow-hidden group bg-gradient-to-r from-red-600/50 to-red-500/30 border border-red-500 text-red-400 hover:from-red-600/70 hover:to-red-500/50 active:from-red-700/80 active:to-red-600/60 transition-all duration-150 font-medium text-sm shadow-lg shadow-red-500/20 hover:shadow-xl hover:shadow-red-500/30 active:shadow-md active:shadow-red-500/40"
                  >
                    <!-- 3D Highlights and Shadows -->
                    <div class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-red-300/30 to-transparent"></div>
                    <div class="absolute top-0 left-0 bottom-0 w-px bg-gradient-to-b from-red-300/30 via-red-300/15 to-transparent"></div>
                    <div class="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-red-900/50 to-transparent"></div>
                    <div class="absolute top-0 right-0 bottom-0 w-px bg-gradient-to-b from-transparent via-red-900/30 to-red-900/50"></div>

                    <span class="relative z-10 transition-transform duration-150 group-active:translate-y-0.5">移除</span>
                    <!-- Button Glow -->
                    <div
                      class="absolute inset-0 bg-gradient-to-r from-red-400/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"
                    ></div>
                  </button>
                </div>

                <!-- Third Row: Info Card Toggle -->
                <div class="flex gap-3 mt-3">
                  <!-- Info Card Toggle Button -->
                  <button
                    @click="toggleModelInfoCard(model)"
                    :class="[
                      'control-btn btn-3d flex-1 h-10 relative overflow-hidden group border transition-all duration-150 font-medium text-sm shadow-lg hover:shadow-xl active:shadow-md',
                      model.showInfoCard
                        ? 'bg-gradient-to-r from-cyan-600/50 to-cyan-500/30 border-cyan-500 text-cyan-300 hover:from-cyan-600/70 hover:to-cyan-500/50 active:from-cyan-700/80 active:to-cyan-600/60 shadow-cyan-500/20 hover:shadow-cyan-500/30 active:shadow-cyan-500/40'
                        : 'bg-gradient-to-r from-slate-600/50 to-slate-500/30 border-slate-500 text-slate-400 hover:from-slate-600/70 hover:to-slate-500/50 active:from-slate-700/80 active:to-slate-600/60 shadow-slate-500/20 hover:shadow-slate-500/30 active:shadow-slate-500/40'
                    ]"
                  >
                    <!-- 3D Highlights and Shadows -->
                    <div :class="[
                      'absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent to-transparent',
                      model.showInfoCard ? 'via-cyan-300/30' : 'via-slate-300/30'
                    ]"></div>
                    <div :class="[
                      'absolute top-0 left-0 bottom-0 w-px bg-gradient-to-b from-transparent to-transparent',
                      model.showInfoCard ? 'via-cyan-300/15' : 'via-slate-300/15'
                    ]"></div>
                    <div :class="[
                      'absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent to-transparent',
                      model.showInfoCard ? 'via-cyan-900/50' : 'via-slate-900/50'
                    ]"></div>
                    <div :class="[
                      'absolute top-0 right-0 bottom-0 w-px bg-gradient-to-b from-transparent to-transparent',
                      model.showInfoCard ? 'via-cyan-900/30 to-cyan-900/50' : 'via-slate-900/30 to-slate-900/50'
                    ]"></div>

                    <span class="relative z-10 transition-transform duration-150 group-active:translate-y-0.5 flex items-center gap-2">
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              :d="model.showInfoCard ? 'M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z' : 'M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21'"></path>
                      </svg>
                      {{ model.showInfoCard ? '隐藏信息卡片' : '显示信息卡片' }}
                    </span>

                    <!-- Button Glow -->
                    <div :class="[
                      'absolute inset-0 bg-gradient-to-r from-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm',
                      model.showInfoCard ? 'from-cyan-400/30' : 'from-slate-400/30'
                    ]"></div>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Sensor Configuration Modal -->
    <div
      v-if="showSensorConfig"
      class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      @click.self="closeSensorConfig"
    >
      <div class="bg-slate-800 border border-cyan-500/30 rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-lg font-bold text-white">传感器配置管理</h3>
          <button
            @click="closeSensorConfig"
            class="text-slate-400 hover:text-white transition-colors"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- 传感器列表 -->
          <div>
            <div class="flex justify-between items-center mb-4">
              <h4 class="text-cyan-300 font-medium">已配置传感器</h4>
              <button
                @click="addNewSensor(props.models.find(m => m.id === showSensorConfig))"
                class="btn-3d px-3 py-2 bg-gradient-to-br from-green-600 to-green-700 hover:from-green-500 hover:to-green-600 active:from-green-700 active:to-green-800 text-white text-sm rounded transition-all duration-150 font-medium shadow-lg shadow-green-500/20 hover:shadow-xl hover:shadow-green-500/30 active:shadow-md active:shadow-green-500/40 relative overflow-hidden group"
              >
                <!-- 3D Highlights and Shadows -->
                <div class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-green-300/40 to-transparent"></div>
                <div class="absolute top-0 left-0 bottom-0 w-px bg-gradient-to-b from-green-300/40 via-green-300/20 to-transparent"></div>
                <div class="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-green-900/60 to-transparent"></div>
                <div class="absolute top-0 right-0 bottom-0 w-px bg-gradient-to-b from-transparent via-green-900/40 to-green-900/60"></div>

                <span class="relative z-10 transition-transform duration-150 group-active:translate-y-0.5">+ 添加传感器</span>
              </button>
            </div>

            <div class="space-y-3 max-h-96 overflow-y-auto">
              <div
                v-for="(sensor, index) in props.models.find(m => m.id === showSensorConfig)?.sensors || []"
                :key="sensor.id"
                class="bg-slate-700/60 border border-slate-600 rounded p-3"
              >
                <div class="flex justify-between items-start mb-2">
                  <div class="flex-1">
                    <div class="flex items-center gap-2 mb-1">
                      <h5 class="text-white font-medium text-sm">{{ sensor.name }}</h5>
                      <button
                        @click="toggleSensorEnabled(sensor)"
                        :class="[
                          'w-4 h-4 rounded border-2 flex items-center justify-center text-xs',
                          sensor.enabled
                            ? 'border-green-500 bg-green-500/20 text-green-400'
                            : 'border-gray-500 bg-gray-500/20 text-gray-400'
                        ]"
                      >
                        ✓
                      </button>
                    </div>
                    <div class="text-xs text-slate-400 space-y-1">
                      <div>位置: ({{ sensor.relativePosition.x }}, {{ sensor.relativePosition.y }}, {{ sensor.relativePosition.z }})</div>
                      <div>俯仰角: {{ sensor.pitchRange.min }}° ~ {{ sensor.pitchRange.max }}°</div>
                      <div>水平角: {{ sensor.yawRange.min }}° ~ {{ sensor.yawRange.max }}°</div>
                      <div>密度: {{ sensor.laserDensity }} 线/度</div>
                    </div>
                  </div>
                  <div class="flex gap-1 ml-2">
                    <button
                      @click="editSensor(props.models.find(m => m.id === showSensorConfig), index)"
                      class="btn-3d px-2 py-1 bg-gradient-to-br from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 active:from-blue-700 active:to-blue-800 text-white text-xs rounded transition-all duration-150 font-medium shadow-md shadow-blue-500/20 hover:shadow-lg hover:shadow-blue-500/30 active:shadow-sm active:shadow-blue-500/40 relative overflow-hidden group"
                    >
                      <span class="relative z-10 transition-transform duration-150 group-active:translate-y-0.5">编辑</span>
                    </button>
                    <button
                      @click="deleteSensor(props.models.find(m => m.id === showSensorConfig), index)"
                      class="btn-3d px-2 py-1 bg-gradient-to-br from-red-600 to-red-700 hover:from-red-500 hover:to-red-600 active:from-red-700 active:to-red-800 text-white text-xs rounded transition-all duration-150 font-medium shadow-md shadow-red-500/20 hover:shadow-lg hover:shadow-red-500/30 active:shadow-sm active:shadow-red-500/40 relative overflow-hidden group"
                    >
                      <span class="relative z-10 transition-transform duration-150 group-active:translate-y-0.5">删除</span>
                    </button>
                  </div>
                </div>
              </div>

              <div
                v-if="!props.models.find(m => m.id === showSensorConfig)?.sensors?.length"
                class="text-center text-slate-400 py-8"
              >
                暂无传感器配置
              </div>
            </div>
          </div>

          <!-- 传感器编辑表单 -->
          <div v-if="editingSensorIndex !== null">
            <h4 class="text-cyan-300 font-medium mb-4">
              {{ editingSensorIndex === -1 ? '添加新传感器' : '编辑传感器' }}
            </h4>

            <div class="space-y-4">
              <!-- Sensor Name -->
              <div>
                <label class="text-xs text-slate-400 block mb-1">传感器名称</label>
                <input
                  v-model="sensorForm.name"
                  type="text"
                  class="w-full px-2 py-1 text-sm bg-slate-700 border border-slate-600 rounded text-white focus:border-cyan-400 focus:outline-none"
                  placeholder="输入传感器名称"
                />
              </div>

              <!-- Relative Position -->
              <div>
                <h5 class="text-cyan-300 text-sm font-medium mb-2">激光相对位置坐标</h5>
                <div class="grid grid-cols-3 gap-2">
                  <div>
                    <label class="text-xs text-slate-400 block mb-1">X</label>
                    <input
                      v-model.number="sensorForm.relativePosition.x"
                      type="number"
                      step="0.1"
                      class="w-full px-2 py-1 text-sm bg-slate-700 border border-slate-600 rounded text-white focus:border-cyan-400 focus:outline-none"
                    />
                  </div>
                  <div>
                    <label class="text-xs text-slate-400 block mb-1">Y</label>
                    <input
                      v-model.number="sensorForm.relativePosition.y"
                      type="number"
                      step="0.1"
                      class="w-full px-2 py-1 text-sm bg-slate-700 border border-slate-600 rounded text-white focus:border-cyan-400 focus:outline-none"
                    />
                  </div>
                  <div>
                    <label class="text-xs text-slate-400 block mb-1">Z</label>
                    <input
                      v-model.number="sensorForm.relativePosition.z"
                      type="number"
                      step="0.1"
                      class="w-full px-2 py-1 text-sm bg-slate-700 border border-slate-600 rounded text-white focus:border-cyan-400 focus:outline-none"
                    />
                  </div>
                </div>
              </div>

              <!-- Pitch Range -->
              <div>
                <h5 class="text-cyan-300 text-sm font-medium mb-2">俯仰角范围 (度)</h5>
                <div class="grid grid-cols-2 gap-2">
                  <div>
                    <label class="text-xs text-slate-400 block mb-1">最小值</label>
                    <input
                      v-model.number="sensorForm.pitchRange.min"
                      type="number"
                      min="-90"
                      max="90"
                      class="w-full px-2 py-1 text-sm bg-slate-700 border border-slate-600 rounded text-white focus:border-cyan-400 focus:outline-none"
                    />
                  </div>
                  <div>
                    <label class="text-xs text-slate-400 block mb-1">最大值</label>
                    <input
                      v-model.number="sensorForm.pitchRange.max"
                      type="number"
                      min="-90"
                      max="90"
                      class="w-full px-2 py-1 text-sm bg-slate-700 border border-slate-600 rounded text-white focus:border-cyan-400 focus:outline-none"
                    />
                  </div>
                </div>
              </div>

              <!-- Yaw Range -->
              <div>
                <h5 class="text-cyan-300 text-sm font-medium mb-2">水平角范围 (度)</h5>
                <div class="grid grid-cols-2 gap-2">
                  <div>
                    <label class="text-xs text-slate-400 block mb-1">最小值</label>
                    <input
                      v-model.number="sensorForm.yawRange.min"
                      type="number"
                      min="-180"
                      max="180"
                      class="w-full px-2 py-1 text-sm bg-slate-700 border border-slate-600 rounded text-white focus:border-cyan-400 focus:outline-none"
                    />
                  </div>
                  <div>
                    <label class="text-xs text-slate-400 block mb-1">最大值</label>
                    <input
                      v-model.number="sensorForm.yawRange.max"
                      type="number"
                      min="-180"
                      max="180"
                      class="w-full px-2 py-1 text-sm bg-slate-700 border border-slate-600 rounded text-white focus:border-cyan-400 focus:outline-none"
                    />
                  </div>
                </div>
              </div>

              <!-- Laser Density -->
              <div>
                <h5 class="text-cyan-300 text-sm font-medium mb-2">激光密度</h5>
                <div>
                  <label class="text-xs text-slate-400 block mb-1">每度激光线数量</label>
                  <input
                    v-model.number="sensorForm.laserDensity"
                    type="number"
                    min="1"
                    max="100"
                    step="1"
                    class="w-full px-2 py-1 text-sm bg-slate-700 border border-slate-600 rounded text-white focus:border-cyan-400 focus:outline-none"
                  />
                </div>
              </div>

              <!-- Enabled Toggle -->
              <div class="flex items-center gap-2">
                <input
                  v-model="sensorForm.enabled"
                  type="checkbox"
                  id="sensorEnabled"
                  class="w-4 h-4 text-cyan-600 bg-slate-700 border-slate-600 rounded focus:ring-cyan-500"
                />
                <label for="sensorEnabled" class="text-sm text-slate-300">启用传感器</label>
              </div>

              <!-- Action Buttons -->
              <div class="flex gap-3 pt-4">
                <button
                  @click="saveSensor(props.models.find(m => m.id === showSensorConfig))"
                  class="btn-3d flex-1 px-4 py-3 bg-gradient-to-br from-green-600 to-green-700 hover:from-green-500 hover:to-green-600 active:from-green-700 active:to-green-800 text-white rounded transition-all duration-150 font-medium shadow-lg shadow-green-500/20 hover:shadow-xl hover:shadow-green-500/30 active:shadow-md active:shadow-green-500/40 relative overflow-hidden group"
                >
                  <!-- 3D Highlights and Shadows -->
                  <div class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-green-300/40 to-transparent"></div>
                  <div class="absolute top-0 left-0 bottom-0 w-px bg-gradient-to-b from-green-300/40 via-green-300/20 to-transparent"></div>
                  <div class="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-green-900/60 to-transparent"></div>
                  <div class="absolute top-0 right-0 bottom-0 w-px bg-gradient-to-b from-transparent via-green-900/40 to-green-900/60"></div>

                  <span class="relative z-10 transition-transform duration-150 group-active:translate-y-0.5">
                    {{ editingSensorIndex === -1 ? '添加传感器' : '保存修改' }}
                  </span>
                </button>
                <button
                  @click="editingSensorIndex = null"
                  class="btn-3d flex-1 px-4 py-3 bg-gradient-to-br from-gray-600 to-gray-700 hover:from-gray-500 hover:to-gray-600 active:from-gray-700 active:to-gray-800 text-white rounded transition-all duration-150 font-medium shadow-lg shadow-gray-500/20 hover:shadow-xl hover:shadow-gray-500/30 active:shadow-md active:shadow-gray-500/40 relative overflow-hidden group"
                >
                  <!-- 3D Highlights and Shadows -->
                  <div class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-300/40 to-transparent"></div>
                  <div class="absolute top-0 left-0 bottom-0 w-px bg-gradient-to-b from-gray-300/40 via-gray-300/20 to-transparent"></div>
                  <div class="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-900/60 to-transparent"></div>
                  <div class="absolute top-0 right-0 bottom-0 w-px bg-gradient-to-b from-transparent via-gray-900/40 to-gray-900/60"></div>

                  <span class="relative z-10 transition-transform duration-150 group-active:translate-y-0.5">取消</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 添加设备弹框 -->
  <AddDeviceModal
    :visible="showAddDeviceModal"
    @close="closeAddDeviceModal"
    @confirm="confirmAddDevice"
  />

  <!-- 添加障碍物弹框 -->
  <AddObstacleModal
    :visible="showAddObstacleModalVisible"
    @close="showAddObstacleModalVisible = false"
    @confirm="handleAddObstacle"
  />
</template>

<style scoped>
/* 自定义滑块样式 */
.slider::-webkit-slider-thumb {
  appearance: none;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: #06b6d4;
  cursor: pointer;
  border: 2px solid #0891b2;
  box-shadow: 0 0 8px rgba(6, 182, 212, 0.5);
}

.slider::-webkit-slider-thumb:hover {
  background: #0891b2;
  box-shadow: 0 0 12px rgba(6, 182, 212, 0.8);
}

.slider::-webkit-slider-track {
  height: 8px;
  border-radius: 4px;
  background: linear-gradient(to right, #334155 0%, #06b6d4 30%, #f59e0b 70%, #f97316 100%);
}

.slider:disabled::-webkit-slider-thumb {
  background: #64748b;
  border-color: #475569;
  box-shadow: none;
  cursor: not-allowed;
}

.slider:disabled::-webkit-slider-track {
  background: #334155;
}

/* Firefox */
.slider::-moz-range-thumb {
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: #06b6d4;
  cursor: pointer;
  border: 2px solid #0891b2;
  box-shadow: 0 0 8px rgba(6, 182, 212, 0.5);
}

.slider::-moz-range-track {
  height: 8px;
  border-radius: 4px;
  background: linear-gradient(to right, #334155 0%, #06b6d4 30%, #f59e0b 70%, #f97316 100%);
}
/* Hide default scrollbar */
.scrollbar-hide::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* Complex clip-path effects that can't be done with Tailwind */
.futuristic-card {
  clip-path: polygon(0 0, calc(100% - 1.5rem) 0, 100% 1.5rem, 100% 100%, 1.5rem 100%, 0 calc(100% - 1.5rem));
}

.model-id-display {
  clip-path: polygon(0 0, calc(100% - 0.5rem) 0, 100% 100%, 0.5rem 100%);
}

.stat-item {
  clip-path: polygon(0 0, calc(100% - 0.5rem) 0, 100% 100%, 0.5rem 100%);
}

.control-btn {
  clip-path: polygon(0 0, calc(100% - 0.75rem) 0, 100% 100%, 0.75rem 100%);
}

.action-btn-left {
  clip-path: polygon(0 0, calc(100% - 0.5rem) 0, 100% 100%, 0.5rem 100%);
  position: relative;
}

.action-btn-right {
  clip-path: polygon(0.5rem 0, 100% 0, calc(100% - 0.5rem) 100%, 0 100%);
  position: relative;
}

/* 3D Button Effects */
.btn-3d {
  transform: translateY(0);
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-3d:hover {
  transform: translateY(-1px);
}

.btn-3d:active {
  transform: translateY(1px);
  transition-duration: 0.05s;
}

/* Enhanced 3D depth with multiple shadows */
.btn-3d::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  right: -2px;
  bottom: -2px;
  background: inherit;
  border-radius: inherit;
  z-index: -1;
  opacity: 0.3;
  filter: blur(4px);
  transition: all 0.15s ease;
}

.btn-3d:hover::before {
  top: 3px;
  left: 3px;
  right: -3px;
  bottom: -3px;
  opacity: 0.4;
}

.btn-3d:active::before {
  top: 1px;
  left: 1px;
  right: -1px;
  bottom: -1px;
  opacity: 0.2;
}

/* Custom animations that extend Tailwind's built-in ones */
@keyframes ping {
  75%,
  100% {
    transform: scale(2);
    opacity: 0;
  }
}
</style>
<style scoped>
.left_part {
  margin: 110px 15px;
}
</style>
