package com.baosight.api.websocket;

import com.baosight.api.udp.UdpDataReceiver;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;

import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.CompletableFuture;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;
import java.util.Random;

/**
 * 路线 WebSocket 处理器
 * 处理路线仿真的实时数据推送和车辆控制
 */
@Component
public class RouteWebSocketHandler implements WebSocketHandler {

    private static final Logger logger = LoggerFactory.getLogger(RouteWebSocketHandler.class);

    @Autowired
    private UdpDataReceiver udpDataReceiver;

    // 存储所有连接的会话
    private final Map<String, WebSocketSession> sessions = new ConcurrentHashMap<>();

    // 存储每个会话的定时任务
    private final Map<String, ScheduledFuture<?>> sessionTasks = new ConcurrentHashMap<>();

    // 存储UDP服务端任务
    private CompletableFuture<Void> udpServerTask;

    // JSON 序列化工具
    private final ObjectMapper objectMapper = new ObjectMapper();

    // 定时任务执行器
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(4);

    // 随机数生成器
    private final Random random = new Random();

    // 模拟车辆数据
    private final List<VehicleData> simulatedVehicles = new ArrayList<>();

    // 车辆数据内部类
    public static class VehicleData {
        public String device_id;
        public Position position;
        public Rotation rotation;
        public int duration;
        public int power;
        public int consume;

        // 默认构造函数（Jackson反序列化需要）
        public VehicleData() {
        }

        public VehicleData(String deviceId, double x, double y, double z,
                          double rx, double ry, double rz, int power, int consume) {
            this.device_id = deviceId;
            this.position = new Position(x, y, z);
            this.rotation = new Rotation(rx, ry, rz);
            this.duration = 1;
            this.power = power;
            this.consume = consume;
        }
    }

    public static class Position {
        public double x, y, z;

        // 默认构造函数（Jackson反序列化需要）
        public Position() {
        }

        public Position(double x, double y, double z) {
            this.x = x; this.y = y; this.z = z;
        }
    }

    public static class Rotation {
        public double x, y, z;

        // 默认构造函数（Jackson反序列化需要）
        public Rotation() {
        }

        public Rotation(double x, double y, double z) {
            this.x = x; this.y = y; this.z = z;
        }
    }

    // 初始化模拟车辆数据
    {
        simulatedVehicles.add(new VehicleData("device_46026", 905, 0, -970, 0, 90, 0, 85, 15));
        simulatedVehicles.add(new VehicleData("device_83055", 800, 0, -800, 0, 45, 0, 92, 8));
        simulatedVehicles.add(new VehicleData("device_86831", 750, 0, -750, 0, 0, 0, 78, 22));
        simulatedVehicles.add(new VehicleData("device_43756", 680, 0, -430, 0, 135, 0, 95, 5));
        simulatedVehicles.add(new VehicleData("device_67239", 600, 0, -600, 0, 180, 0, 67, 33));
    }

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String sessionId = session.getId();
        sessions.put(sessionId, session);
        logger.info("🔗 WebSocket 连接建立: {}", sessionId);

        // 发送连接成功消息
        Map<String, Object> welcomeMessage = Map.of(
            "type", "connection",
            "action", "connected",
            "data", Map.of(
                "message", "WebSocket 连接已建立",
                "timestamp", System.currentTimeMillis(),
                "sessionId", sessionId
            )
        );
        sendMessage(session, welcomeMessage);

        // 启动UDP服务端（如果还未启动）
        startUdpServerIfNeeded();

        // 开始定时发送车辆控制电文
        //startSendingVehicleCommands(session);

        logger.info("✅ 会话 {} 初始化完成，开始发送车辆控制电文", sessionId);
    }

    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        String sessionId = session.getId();
        String payload = message.getPayload().toString();

        try {
            // 尝试解析为车辆状态数组
            if (payload.trim().startsWith("[")) {
                List<VehicleData> vehicleStatusList = objectMapper.readValue(payload,
                    new TypeReference<List<VehicleData>>() {});
                handleVehicleStatusUpdate(session, vehicleStatusList);
                return;
            }

            // 解析为普通消息对象
            Map<String, Object> clientMessage = objectMapper.readValue(payload, Map.class);
            String type = (String) clientMessage.get("type");

            logger.info("📨 收到来自 {} 的消息类型: {}", sessionId, type);

            switch (type) {
                case "test":
                    handleTestMessage(session, clientMessage);
                    break;
                case "request":
                    handleRequestMessage(session, clientMessage);
                    break;
                case "connection":
                    handleConnectionMessage(session, clientMessage);
                    break;
                default:
                    logger.warn("⚠️ 未知的消息类型: {}", type);
            }
        } catch (Exception e) {
            logger.error("处理消息时发生错误: {}", e.getMessage(), e);
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        String sessionId = session.getId();
        logger.error("WebSocket 传输错误 {}: {}", sessionId, exception.getMessage(), exception);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        String sessionId = session.getId();
        sessions.remove(sessionId);

        // 取消该会话的定时任务
        ScheduledFuture<?> task = sessionTasks.remove(sessionId);
        if (task != null && !task.isCancelled()) {
            task.cancel(true);
        }

        // 如果没有活跃会话，停止UDP服务端
        if (sessions.isEmpty()) {
            stopUdpServer();
        }

        logger.info("🔌 WebSocket 连接关闭: {}, 状态: {}", sessionId, closeStatus);
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }

    /**
     * 处理车辆状态更新
     */
    private void handleVehicleStatusUpdate(WebSocketSession session, List<VehicleData> vehicleStatusList) {
        String sessionId = session.getId();
        logger.info("📊 收到来自 {} 的车辆状态信息: {} 辆车", sessionId, vehicleStatusList.size());

        for (VehicleData vehicle : vehicleStatusList) {
            logger.debug("  - 车辆 {}: 位置({}, {}, {}), 角度({}, {}, {}), 电量: {}%, 消耗: {}",
                vehicle.device_id,
                Math.round(vehicle.position.x), Math.round(vehicle.position.y), Math.round(vehicle.position.z),
                Math.round(vehicle.rotation.x), Math.round(vehicle.rotation.y), Math.round(vehicle.rotation.z),
                vehicle.power, vehicle.consume);
        }
    }

    /**
     * 处理测试消息
     */
    private void handleTestMessage(WebSocketSession session, Map<String, Object> message) {
        logger.info("🧪 收到测试消息: {}", message.get("data"));

        Map<String, Object> response = Map.of(
            "type", "test",
            "action", "pong",
            "data", Map.of(
                "message", "测试消息已收到",
                "timestamp", System.currentTimeMillis(),
                "originalMessage", message.get("data")
            )
        );
        sendMessage(session, response);
    }

    /**
     * 处理请求消息
     */
    private void handleRequestMessage(WebSocketSession session, Map<String, Object> message) {
        Map<String, Object> data = (Map<String, Object>) message.get("data");
        String action = (String) message.get("action");

        logger.info("📤 收到请求: {}", action);

        if ("route_data".equals(action)) {
            // 发送批量路线数据
            sendMessage(session, Map.of(
                "type", "route_data",
                "action", "batch",
                "data", generateVehicleCommands()
            ));
        }
    }

    /**
     * 处理连接消息
     */
    private void handleConnectionMessage(WebSocketSession session, Map<String, Object> message) {
        logger.info("🔗 收到连接消息: {}", message.get("action"));
    }

    /**
     * 启动UDP服务端（如果需要）
     */
    private void startUdpServerIfNeeded() {
        if (!udpDataReceiver.isRunning()) {
            logger.info("🚀 启动UDP服务端，端口: {}", udpDataReceiver.getUdpPort());

            udpServerTask = udpDataReceiver.startServer(this::handleUdpData);

            udpServerTask.whenComplete((result, throwable) -> {
                if (throwable != null) {
                    logger.error("UDP服务端启动失败: {}", throwable.getMessage(), throwable);
                } else {
                    logger.info("✅ UDP服务端启动完成");
                }
            });
        }
    }

    /**
     * 停止UDP服务端
     */
    private void stopUdpServer() {
        if (udpDataReceiver.isRunning()) {
            logger.info("🛑 停止UDP服务端");
            udpDataReceiver.stopServer();

            if (udpServerTask != null && !udpServerTask.isDone()) {
                udpServerTask.cancel(true);
            }
        }
    }

    /**
     * 处理UDP接收到的数据
     */
    private void handleUdpData(String udpData) {
        try {
            logger.debug("📨 处理UDP数据: {}", udpData);

            // 直接将UDP数据转发给所有WebSocket客户端
            if (!sessions.isEmpty()) {
                // 尝试解析为JSON，如果失败则作为原始字符串发送
                Object dataToSend = null;
                try {
                    dataToSend = objectMapper.readValue(udpData, Object.class);
                } catch (Exception e) {
                    // 如果不是有效的JSON，包装成消息对象
                    dataToSend = Map.of(
                        "type", "udp_data",
                        "action", "raw",
                        "data", udpData,
                        "timestamp", System.currentTimeMillis()
                    );
                }

                // 将数据转换为JSON字符串，避免在lambda中重复序列化
                final String jsonToSend = objectMapper.writeValueAsString(dataToSend);

                // 广播给所有连接的客户端
                sessions.values().forEach(session -> {
                    try {
                        if (session.isOpen()) {
                            session.sendMessage(new TextMessage(jsonToSend));
                        }
                    } catch (Exception e) {
                        logger.error("转发UDP数据到WebSocket失败: {}", e.getMessage(), e);
                    }
                });

                logger.debug("📤 UDP数据已转发给 {} 个WebSocket客户端", sessions.size());
            }
        } catch (Exception e) {
            logger.error("处理UDP数据时发生错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 开始发送车辆控制电文
     */
    private void startSendingVehicleCommands(WebSocketSession session) {
        String sessionId = session.getId();

        // 每秒发送一次车辆控制电文
        ScheduledFuture<?> task = scheduler.scheduleAtFixedRate(() -> {
            if (session.isOpen()) {
                try {
                    List<VehicleData> commands = generateVehicleCommands();
                    String json = objectMapper.writeValueAsString(commands);
                    session.sendMessage(new TextMessage(json));

                    logger.debug("📤 发送车辆控制电文给会话 {}: {} 辆车", sessionId, commands.size());
                } catch (Exception e) {
                    logger.error("发送车辆控制电文时发生错误: {}", e.getMessage(), e);
                }
            }
        }, 2, 1, TimeUnit.SECONDS); // 2秒后开始，每1秒发送一次

        sessionTasks.put(sessionId, task);
        logger.info("⏰ 为会话 {} 启动定时发送任务", sessionId);
    }

    /**
     * 生成车辆控制电文
     */
    private List<VehicleData> generateVehicleCommands() {
        List<VehicleData> commands = new ArrayList<>();

        for (VehicleData vehicle : simulatedVehicles) {
            // 创建车辆控制命令的副本
            VehicleData command = new VehicleData(
                vehicle.device_id,
                vehicle.position.x,
                vehicle.position.y,
                vehicle.position.z,
                vehicle.rotation.x,
                vehicle.rotation.y,
                vehicle.rotation.z,
                vehicle.power,
                vehicle.consume
            );

            // 模拟车辆移动 - 随机小幅度移动
            command.position.x += (random.nextDouble() - 0.5) * 10; // ±5单位随机移动
            command.position.z += (random.nextDouble() - 0.5) * 10;

            // 模拟车辆旋转 - 随机小幅度旋转
            command.rotation.y += (random.nextDouble() - 0.5) * 20; // ±10度随机旋转

            // 模拟电量消耗
            if (random.nextDouble() < 0.3) { // 30%概率消耗电量
                command.power = Math.max(0, command.power - random.nextInt(3));
                command.consume = Math.min(100, command.consume + random.nextInt(2));
            }

            // 更新原始数据
            vehicle.position.x = command.position.x;
            vehicle.position.z = command.position.z;
            vehicle.rotation.y = command.rotation.y;
            vehicle.power = command.power;
            vehicle.consume = command.consume;

            commands.add(command);
        }

        return commands;
    }

    /**
     * 发送消息到客户端
     */
    private void sendMessage(WebSocketSession session, Map<String, Object> message) {
        try {
            if (session.isOpen()) {
                String json = objectMapper.writeValueAsString(message);
                session.sendMessage(new TextMessage(json));
            }
        } catch (IOException e) {
            logger.error("发送消息失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 广播车辆控制电文到所有连接的客户端
     */
    public void broadcastVehicleCommands() {
        if (!sessions.isEmpty()) {
            List<VehicleData> commands = generateVehicleCommands();
            sessions.values().forEach(session -> {
                try {
                    if (session.isOpen()) {
                        String json = objectMapper.writeValueAsString(commands);
                        session.sendMessage(new TextMessage(json));
                    }
                } catch (Exception e) {
                    logger.error("广播消息失败: {}", e.getMessage(), e);
                }
            });
            logger.info("📡 广播车辆控制电文到 {} 个客户端", sessions.size());
        }
    }

    /**
     * 广播消息到所有连接的客户端
     */
    public void broadcastMessage(Map<String, Object> message) {
        sessions.values().forEach(session -> sendMessage(session, message));
    }

    /**
     * 获取当前连接数
     */
    public int getConnectionCount() {
        return sessions.size();
    }

    /**
     * 获取模拟车辆数据
     */
    public List<VehicleData> getSimulatedVehicles() {
        return new ArrayList<>(simulatedVehicles);
    }

    /**
     * 更新车辆数据
     */
    public void updateVehicleData(String deviceId, double x, double z, double rotationY, int power, int consume) {
        simulatedVehicles.stream()
            .filter(v -> v.device_id.equals(deviceId))
            .findFirst()
            .ifPresent(v -> {
                v.position.x = x;
                v.position.z = z;
                v.rotation.y = rotationY;
                v.power = power;
                v.consume = consume;
            });
    }
}
